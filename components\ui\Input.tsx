import React from 'react';
import { View, TextInput, Text, TextInputProps, ViewStyle, TextStyle } from 'react-native';
// Removed incorrect twrnc import

interface InputProps extends TextInputProps {
  label?: string; // Optional label
  error?: string | boolean; // Error message or boolean flag
  containerStyle?: ViewStyle;
  labelStyle?: TextStyle;
  inputStyle?: ViewStyle & TextStyle; // Combine ViewStyle and TextStyle for input
  errorStyle?: TextStyle;
}

const Input: React.FC<InputProps> = ({
  label,
  error,
  placeholder,
  value,
  onChangeText,
  secureTextEntry = false,
  keyboardType = 'default',
  containerStyle,
  labelStyle,
  inputStyle,
  errorStyle,
  ...props // Pass remaining TextInput props
}) => {
  const baseInputClasses = 'border p-3 rounded-md text-base';
  // Note: focus pseudo-classes might need web adaptation or custom logic in RN. Using border color change for now.
  const focusInputClasses = 'focus:border-primary-500';
  const errorInputClasses = 'border-feedback-error';
  const normalInputClasses = 'border-border bg-input-background text-text-primary';

  const inputClasses = `
    ${baseInputClasses}
    ${error ? errorInputClasses : normalInputClasses}
    ${focusInputClasses}
  `;

  const baseLabelClasses = 'text-sm font-medium mb-1 text-text-secondary';
  const baseErrorClasses = 'text-sm text-feedback-error mt-1';

  return (
    <View style={containerStyle} className="mb-4">
      {label && (
        <Text style={labelStyle} className={baseLabelClasses}>
          {label}
        </Text>
      )}
      <TextInput
        placeholder={placeholder}
        value={value}
        onChangeText={onChangeText}
        secureTextEntry={secureTextEntry}
        keyboardType={keyboardType}
        className={inputClasses}
        style={inputStyle} // Allow overriding styles
        placeholderTextColor="text-secondary" // Using theme color
        {...props}
      />
      {error && typeof error === 'string' && (
        <Text style={errorStyle} className={baseErrorClasses}>
          {error}
        </Text>
      )}
    </View>
  );
};

export default Input;