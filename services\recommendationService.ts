import { getFunctions, httpsCallable, HttpsCallableResult } from 'firebase/functions';
import { getFirestore, collection, addDoc, serverTimestamp, query, where, orderBy, getDocs, doc, deleteDoc } from 'firebase/firestore';
import { app } from '../firebaseConfig';

interface RecommendationFeedbackData {
  userId: string;
  profileId: string;
  recommendationId: string;
  feedbackType: 'like' | 'dislike';
  recommendationDetails?: {
    name: string;
    description: string;
    [key: string]: any;
  };
}

export interface GiftRecommendation {
  id: string; // Add required ID for tracking
  name: string;
  description: string;
  priceRange: string;
  categories: string[];
  recommendationId?: string; // Add optional ID for tracking
}

interface RecommendationsObject {
  recommendations?: GiftRecommendation[];
}

interface DataObject {
  data?: GiftRecommendation[];
}

interface SuccessResponse {
  success: boolean;
  recommendations?: GiftRecommendation[];
}

type RecommendationResponse =
  | GiftRecommendation[]
  | RecommendationsObject
  | DataObject
  | SuccessResponse;


export interface FeedbackEntry {
  id: string;
  userId: string;
  profileId: string;
  recommendationId: string;
  feedbackType: 'like' | 'dislike';
  recommendationDetails?: {
    name: string;
    description: string;
    [key: string]: any;
  };
  timestamp: any; // Firestore Timestamp or serverTimestamp
}

const functions = getFunctions(app);

export const getProfileFeedback = async (profileId: string): Promise<FeedbackEntry[]> => {
  try {
    const db = getFirestore(app);
    const feedbackCollection = collection(db, 'recommendation_feedback');
    
    // Create query to filter by profileId and order by timestamp descending
    const feedbackQuery = query(
      feedbackCollection,
      where('profileId', '==', profileId),
      orderBy('timestamp', 'desc')
    );

    const querySnapshot = await getDocs(feedbackQuery);
    
    // Map each document to include the document id
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...(doc.data() as Omit<FeedbackEntry, 'id'>)
    }));
  } catch (error) {
    console.error('Error fetching profile feedback:', error);
    throw error;
  }
};

export const deleteRecommendationFeedback = async (feedbackId: string): Promise<boolean> => {
  try {
    const db = getFirestore(app);
    const feedbackDoc = doc(db, 'recommendation_feedback', feedbackId);
    await deleteDoc(feedbackDoc);
    return true;
  } catch (error) {
    console.error('Error deleting recommendation feedback:', error);
    return false;
  }
};
export const fetchGiftRecommendations = async (
  profileId: string,
  occasion?: string,
  date?: string
): Promise<GiftRecommendation[]> => {
  try {
    const getRecommendations = httpsCallable<
      { profileId: string; occasion?: string; date?: string },
      RecommendationResponse
    >(
      functions,
      'getGiftRecommendations'
    );
    const result = await getRecommendations({ profileId, occasion, date });
    console.log('Recommendations response:', result.data);

    // Handle different response formats with proper type guards
    if (Array.isArray(result.data)) {
      return result.data;
    }
    
    const response = result.data as Record<string, any>;
    if (response?.recommendations) {
      return response.recommendations;
    }
    if (response?.data) {
      return response.data;
    }
    
    return [];
  } catch (error) {
    console.error('Error calling getGiftRecommendations:', error);
    throw error;
  }
};

export const callGetGenericRecommendations = async (
  query: string
): Promise<GiftRecommendation[]> => {
  try {
    const getRecommendations = httpsCallable<
      { query: string }, // Only pass query for generic search
      RecommendationResponse
    >(
      functions,
      'getGiftRecommendations'
    );
    const result = await getRecommendations({ query });
    console.log('Generic Recommendations response:', result.data);

    // Handle different response formats with proper type guards
    if (Array.isArray(result.data)) {
      return result.data;
    }
    
    const response = result.data as Record<string, any>;
    if (response?.recommendations) {
      return response.recommendations;
    }
    if (response?.data) {
      return response.data;
    }
    
    return [];
  } catch (error) {
    console.error('Error calling getGiftRecommendations (generic):', error);
    throw error;
  }
};
  
export const saveRecommendationFeedback = async (
  feedbackData: RecommendationFeedbackData
): Promise<boolean> => {
  try {
    // Validate required fields
    if (!feedbackData.userId || !feedbackData.profileId || !feedbackData.recommendationId) {
      console.error('Missing required fields for feedback:', feedbackData);
      return false;
    }

    const db = getFirestore(app);
    const feedbackCollection = collection(db, 'recommendation_feedback');
    
    // Create document with explicit field mapping
    const docData = {
      userId: feedbackData.userId,
      profileId: feedbackData.profileId,
      recommendationId: feedbackData.recommendationId,
      feedbackType: feedbackData.feedbackType,
      recommendationDetails: feedbackData.recommendationDetails || null,
      timestamp: serverTimestamp()
    };

    console.log('Saving feedback with data:', docData);
    await addDoc(feedbackCollection, docData);
    
    return true;
  } catch (error) {
    console.error('Error saving recommendation feedback:', error);
    return false;
  }
};