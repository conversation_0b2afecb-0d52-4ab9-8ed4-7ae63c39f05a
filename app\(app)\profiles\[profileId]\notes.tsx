import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import Animated, { FadeIn, SlideInRight } from 'react-native-reanimated';
import { useColorScheme } from 'nativewind';
import { format } from 'date-fns';

import { useAuth } from '../../../../contexts/AuthContext';
import {
  getSignificantOtherById,
  updateSignificantOther
} from '../../../../services/profileService';
import { SignificantOtherProfile, GeneralNote } from '../../../../functions/src/types/firestore';
import LoadingIndicator from '../../../../components/ui/LoadingIndicator';
import Card from '../../../../components/ui/Card';
import Button from '../../../../components/ui/Button';

const NotesScreen = () => {
  const { profileId } = useLocalSearchParams();
  const router = useRouter();
  const { user } = useAuth();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [profile, setProfile] = useState<SignificantOtherProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deletingIds, setDeletingIds] = useState<Set<string>>(new Set());

  const id = Array.isArray(profileId) ? profileId[0] : profileId;

  const fetchData = useCallback(async () => {
    if (!user?.uid || !id) return;

    try {
      setLoading(true);
      setError(null);

      const profileData = await getSignificantOtherById(user.uid, id);
      setProfile(profileData);
    } catch (err) {
      setError('Failed to load notes. Please try again.');
      console.error('Error fetching notes:', err);
    } finally {
      setLoading(false);
    }
  }, [user?.uid, id]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleDeleteNote = async (noteIndex: number) => {
    if (!profile || !user?.uid) return;

    const note = profile.generalNotes?.[noteIndex];
    if (!note) return;

    Alert.alert(
      'Delete Note',
      'Are you sure you want to delete this note?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const noteId = `note-${noteIndex}`;
            setDeletingIds(prev => new Set(prev).add(noteId));

            try {
              const updatedNotes = profile.generalNotes?.filter((_, index) => index !== noteIndex) || [];
              await updateSignificantOther(user.uid, id, {
                generalNotes: updatedNotes,
              });

              setProfile(prev => prev ? { ...prev, generalNotes: updatedNotes } : null);
            } catch (err) {
              Alert.alert('Error', 'Failed to delete note. Please try again.');
            } finally {
              setDeletingIds(prev => {
                const newSet = new Set(prev);
                newSet.delete(noteId);
                return newSet;
              });
            }
          },
        },
      ]
    );
  };

  const renderNoteItem = ({ item, index }: { item: GeneralNote; index: number }) => {
    const noteId = `note-${index}`;
    const isDeleting = deletingIds.has(noteId);

    return (
      <Animated.View
        entering={SlideInRight.delay(index * 100).duration(400)}
        className="mb-4"
      >
        <Card className="p-4">
          <View className="flex-row items-start justify-between">
            <View className="flex-1 mr-3">
              <Text className="mb-2 text-base leading-relaxed text-text-primary dark:text-text-primary-dark">
                {item.note}
              </Text>

              {item.date && (
                <Text className="text-xs text-text-secondary dark:text-text-secondary-dark">
                  {format(item.date.toDate(), 'MMM d, yyyy')}
                </Text>
              )}
            </View>

            <TouchableOpacity
              onPress={() => handleDeleteNote(index)}
              disabled={isDeleting}
              className="p-2 rounded-full active:bg-error/10"
            >
              {isDeleting ? (
                <LoadingIndicator size="small" color="#DC2626" />
              ) : (
                <Feather name="trash-2" size={18} color="#DC2626" />
              )}
            </TouchableOpacity>
          </View>
        </Card>
      </Animated.View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <View className="items-center justify-center flex-1">
          <LoadingIndicator size="large" color="#A3002B" />
          <Text className="mt-4 text-base text-text-secondary dark:text-text-secondary-dark">
            Loading notes...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <View className="items-center justify-center flex-1 px-6">
          <Feather name="alert-circle" size={48} color="#DC2626" />
          <Text className="mt-4 text-lg font-semibold text-center text-error dark:text-error-dark">
            {error}
          </Text>
          <Button
            title="Try Again"
            onPress={fetchData}
            variant="primary"
            className="mt-4"
          />
        </View>
      </SafeAreaView>
    );
  }

  const notes = profile?.generalNotes || [];

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
      <View className="flex-1 px-4 py-6">
        {profile && (
          <View className="mb-6">
            <Text className="text-2xl font-bold text-text-primary dark:text-text-primary-dark">
              {profile.name}'s Notes
            </Text>
            <Text className="text-base text-text-secondary dark:text-text-secondary-dark">
              Personal insights and important details
            </Text>
          </View>
        )}

        {notes.length === 0 ? (
          <Animated.View
            entering={FadeIn.duration(600)}
            className="items-center justify-center flex-1 px-6"
          >
            <View className="items-center justify-center w-20 h-20 mb-6 rounded-full bg-primary/10 dark:bg-primary-dark/10">
              <Feather
                name="file-text"
                size={32}
                color={isDark ? '#C70039' : '#A3002B'}
              />
            </View>
            <Text className="mb-2 text-lg font-semibold text-center text-text-primary dark:text-text-primary-dark">
              No Notes Yet
            </Text>
            <Text className="mb-6 text-base text-center text-text-secondary dark:text-text-secondary-dark">
              Add notes to remember important details about {profile?.name}
            </Text>
            <Button
              title="Add First Note"
              onPress={() => router.push(`/profiles/${id}/edit?focusSection=generalNotes`)}
              variant="primary"
            />
          </Animated.View>
        ) : (
          <FlatList
            data={notes}
            renderItem={renderNoteItem}
            keyExtractor={(_, index) => `note-${index}`}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 20 }}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

export default NotesScreen;
