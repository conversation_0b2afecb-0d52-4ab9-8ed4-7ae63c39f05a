rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // De<PERSON>ult deny all access
    match /{document=**} {
      allow read, write: if false;
    }

    // Allow users to manage their own document in a 'users' collection
    match /users/{userId} {
      // Allow read and update if the user is authenticated and the UID matches the document ID
      allow read, update: if request.auth != null && request.auth.uid == userId;
      // Allow create if the user is authenticated (they can create their own document)
      allow create: if request.auth != null;
      // Deny delete for now
      allow delete: if false;
    }

    match /significant_others/{profileId} {
      // Allow read/write only if the user is authenticated and owns the profile
      allow read, delete: if request.auth != null && resource.data.userId == request.auth.uid;
      allow update: if request.auth != null && resource.data.userId == request.auth.uid && (!('userId' in request.resource.data) || request.resource.data.userId == resource.data.userId);
      // Allow create if the user is authenticated and the userId matches the authenticated user's UID
      allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;
    }

    match /recommendation_feedback/{feedbackId} {
      // Allow create only for authenticated users and ensure userId matches auth.uid
      allow create: if request.auth != null
        && request.resource.data.userId == request.auth.uid
        && request.resource.data.profileId is string
        && request.resource.data.recommendationId is string
        && request.resource.data.feedbackType is string
        && (request.resource.data.feedbackType == "like" || request.resource.data.feedbackType == "dislike")
        && request.resource.data.timestamp is timestamp;
      // Allow read/delete if:
      // - User is authenticated AND
      // - Either feedback belongs to user OR profile belongs to user
      allow read, delete: if request.auth != null
        && (resource.data.userId == request.auth.uid
            || get(/databases/$(database)/documents/significant_others/$(resource.data.profileId)).data.userId == request.auth.uid);
      allow update: if false;
    }
  }

  // Corrected placement for pushTokens
  match /pushTokens/{tokenId} {
    allow read, delete: if request.auth != null && request.auth.uid == resource.data.userId;
    allow update: if request.auth != null && request.auth.uid == resource.data.userId && (!('userId' in request.resource.data) || request.resource.data.userId == resource.data.userId);
    allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
  }

  // Corrected placement for userSettings
  match /userSettings/{userId} {
    allow read: if request.auth != null && request.auth.uid == userId;
    allow create: if request.auth != null && request.auth.uid == userId && request.resource.data.userId == request.auth.uid; // Ensure doc field matches
    allow update: if request.auth != null && request.auth.uid == userId && (!('userId' in request.resource.data) || request.resource.data.userId == userId); // userId field cannot change
    allow delete: if request.auth != null && request.auth.uid == userId; // Or set to false if not deletable
  }
}