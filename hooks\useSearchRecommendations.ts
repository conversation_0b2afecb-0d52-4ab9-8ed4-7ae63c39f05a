import { useState } from 'react';
import { GiftRecommendation, callGetGenericRecommendations } from '../services/recommendationService';

interface UseSearchRecommendations {
  recommendations: GiftRecommendation[] | null;
  isGenerating: boolean;
  recommendationError: Error | null;
  fetchRecommendations: (query: string) => Promise<void>;
}

export const useSearchRecommendations = (): UseSearchRecommendations => {
  const [recommendations, setRecommendations] = useState<GiftRecommendation[] | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [recommendationError, setRecommendationError] = useState<Error | null>(null);

  const fetchRecommendations = async (query: string) => {
    setIsGenerating(true);
    setRecommendationError(null);
    setRecommendations(null); // Clear previous recommendations

    try {
      const fetchedRecommendations = await callGetGenericRecommendations(query);
      setRecommendations(fetchedRecommendations);
    } catch (error: any) {
      console.error('Error fetching search recommendations:', error);
      setRecommendationError(error);
    } finally {
      setIsGenerating(false);
    }
  };

  return {
    recommendations,
    isGenerating,
    recommendationError,
    fetchRecommendations,
  };
};