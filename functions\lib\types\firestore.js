"use strict";
// types/firestore.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.userProfileSchema = exports.significantOtherProfileSchema = void 0;
// --- Zod Schema for Runtime Validation ---
const zod_1 = require("zod");
const firestore_1 = require("firebase-admin/firestore"); // Use admin timestamp
// Helper schema for Firestore Timestamp
const zodTimestamp = zod_1.z.custom((val) => val instanceof firestore_1.Timestamp, {
    message: "Expected Firestore Timestamp",
});
const zodWishlistItem = zod_1.z.object({
    item: zod_1.z.string().min(1),
    notes: zod_1.z.string().optional(),
    // Allow empty string or valid URL for link
    link: zod_1.z.string().url().optional().or(zod_1.z.literal("")),
    dateAdded: zodTimestamp.nullable(), // Allow null for dateAdded in Zod schema
});
const zodPastGiftGiven = zod_1.z.object({
    item: zod_1.z.string().min(1),
    occasion: zod_1.z.string().optional(),
    date: zodTimestamp.nullable(),
    reaction: zod_1.z.string().optional(),
});
const zodGeneralNote = zod_1.z.object({
    note: zod_1.z.string().min(1),
    date: zodTimestamp.nullable(),
});
const zodCustomDate = zod_1.z.object({
    id: zod_1.z.string().min(1),
    name: zod_1.z.string().min(1),
    date: zodTimestamp.nullable(),
    customDateMonthDay: zod_1.z.string().regex(/^\d{2}-\d{2}$/).optional(),
});
exports.significantOtherProfileSchema = zod_1.z.object({
    userId: zod_1.z.string().min(1),
    profileId: zod_1.z.string().min(1),
    name: zod_1.z.string().min(1),
    relationship: zod_1.z.string(),
    birthday: zodTimestamp.nullable().optional(),
    anniversary: zodTimestamp.nullable().optional(),
    // MM-DD format validation
    birthdayMonthDay: zod_1.z.string().regex(/^\d{2}-\d{2}$/).optional(),
    anniversaryMonthDay: zod_1.z.string().regex(/^\d{2}-\d{2}$/).optional(),
    interests: zod_1.z.array(zod_1.z.string()),
    dislikes: zod_1.z.array(zod_1.z.string()),
    preferences: zod_1.z.object({
        favoriteColor: zod_1.z.string().nullable().optional(),
        preferredStyle: zod_1.z.string().nullable().optional(),
        favoriteBrands: zod_1.z.array(zod_1.z.string()).optional(),
    }).passthrough(),
    sizes: zod_1.z.object({
        clothing: zod_1.z.string().nullable().optional(),
        shoe: zod_1.z.string().nullable().optional(),
    }).passthrough(),
    wishlistItems: zod_1.z.array(zodWishlistItem),
    pastGiftsGiven: zod_1.z.array(zodPastGiftGiven),
    generalNotes: zod_1.z.array(zodGeneralNote),
    customDates: zod_1.z.array(zodCustomDate).optional(),
    createdAt: zodTimestamp,
    updatedAt: zodTimestamp,
});
// --- Zod Schema for User Profile ---
exports.userProfileSchema = zod_1.z.object({
    // Assuming userId is the document ID, not stored in the doc itself
    // Add fields expected in user_profiles based on SOProfileForPrompt usage
    interests: zod_1.z.array(zod_1.z.string()).optional().nullable(),
    dislikes: zod_1.z.array(zod_1.z.string()).optional().nullable(),
    preferences: zod_1.z.record(zod_1.z.string()).optional().nullable(),
    // Assuming pastGiftsGiven might be simpler here, maybe just strings?
    // Adjust if the structure is different (e.g., objects like in SOProfile)
    pastGiftsGiven: zod_1.z.array(zod_1.z.string()).optional().nullable(),
    // Add other expected fields like name, email if stored here
    // createdAt: zodTimestamp.optional(), // Optional if not always present
    // updatedAt: zodTimestamp.optional(),
}).passthrough(); // Allow other fields not explicitly defined
//# sourceMappingURL=firestore.js.map