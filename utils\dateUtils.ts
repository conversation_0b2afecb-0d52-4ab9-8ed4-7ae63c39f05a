// utils/dateUtils.ts
import { Timestamp } from 'firebase/firestore';

export const formatDateForDisplay = (dateInput: Date | Timestamp | null | undefined): string => {
  if (!dateInput) {
    return "Select Date";
  }

  try {
    const dateObject = (dateInput instanceof Timestamp) ? dateInput.toDate() : dateInput;

    // Check if the date is valid
    if (!(dateObject instanceof Date) || isNaN(dateObject.getTime())) {
      return "Invalid Date";
    }

    return dateObject.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  } catch (error) {
    console.warn('Error formatting date for display:', dateInput, error);
    return "Invalid Date";
  }
};