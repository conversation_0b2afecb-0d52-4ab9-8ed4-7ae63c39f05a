import { View, Text, TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { WishlistItem } from '../../functions/src/types/firestore';

interface WishlistItemFormInputProps {
  item: WishlistItem;
  onRemove: () => void;
  index: number;
}

export const WishlistItemFormInput = ({
  item,
  onRemove,
  index,
}: WishlistItemFormInputProps) => {
  const formatDate = (timestamp: any) => {
    if (!timestamp) return '';
    const date = timestamp.toDate();
    return date.toLocaleDateString();
  };

  return (
    <View className="p-3 mb-2 border rounded-md border-border bg-background">
      <View className="flex-row justify-between">
        <View className="flex-1">
          <Text className="font-semibold text-foreground">
            {item.item}
          </Text>
          {item.notes ? (
            <Text className="text-sm text-muted-foreground">
              {item.notes}
            </Text>
          ) : null}
          {item.link ? (
            <Text className="text-sm text-muted-foreground">
              {item.link}
            </Text>
          ) : null}
          {item.dateAdded ? (
            <Text className="text-xs text-muted-foreground">
              Added: {formatDate(item.dateAdded)}
            </Text>
          ) : null}
        </View>
        <TouchableOpacity
          onPress={onRemove}
          accessibilityLabel="Remove wishlist item"
        >
          <Feather name="trash-2" size={20} color="text-destructive" />
        </TouchableOpacity>
      </View>
    </View>
  );
};