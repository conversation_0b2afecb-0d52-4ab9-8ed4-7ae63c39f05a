import React from 'react';
import { View } from 'react-native';
import { Control, FieldErrors } from 'react-hook-form';
import { Feather } from '@expo/vector-icons';
import { z } from 'zod';
import { Timestamp } from 'firebase/firestore'; // Keep Timestamp for schema
import Accordion from '@/components/ui/Accordion';
import BasicInfoSection from './BasicInfoSection';
import InterestsDislikesSection from './InterestsDislikesSection';
import PreferencesSection from './PreferencesSection';
import SizesSection from './SizesSection';
import WishlistSection from './WishlistSection';
import PastGiftsSection from './PastGiftsSection';
import GeneralNotesSection from './GeneralNotesSection';
import CustomDatesSection from './CustomDatesSection';
import { WishlistItem, PastGiftGiven, GeneralNote } from '@/functions/src/types/firestore'; // Keep types for schema

// Moved profileSchema definition here as per instructions
export const profileSchema = z.object({
  name: z.string().min(1, { message: "Name is required" }),
  relationship: z.string().min(1, { message: "Relationship is required" }),
  birthday: z.date().nullable().optional(),
  anniversary: z.date().nullable().optional(),
  interestsInput: z.string().optional(),
  dislikesInput: z.string().optional(),
  // Removed redundant top-level preference fields
  clothingSize: z.string().optional(),
  shoeSize: z.string().optional(),
  wishlistItems: z.array(z.object({
    item: z.string().min(1),
    notes: z.string().optional(),
    link: z.string().url().optional().or(z.literal('')),
    dateAdded: z.date().nullable().optional() // Changed to Date | null
  })).optional().default([]),
  pastGiftsGiven: z.array(z.object({
    item: z.string().min(1),
    occasion: z.string().optional(),
    date: z.date().nullable().optional(), // Changed to Date | null
    reaction: z.string().optional()
  })).optional().default([]),
  generalNotes: z.array(z.object({
    note: z.string().min(1),
    date: z.date().nullable().optional() // Changed to Date | null
  })).optional().default([]),
  customDates: z.array(z.object({ id: z.string(), name: z.string().min(1), date: z.date().nullable() })).optional().default([]),
  preferences: z.object({
    favoriteColor: z.string().optional(),
    preferredStyle: z.string().optional(),
    favoriteBrands: z.array(z.string()).optional().default([]), // Changed to string[]
    budgetMin: z.preprocess(
      (val) => (val === "" || val === null || val === undefined ? undefined : parseFloat(String(val))),
      z.number({ invalid_type_error: 'Min budget must be a number.' }).positive({ message: 'Min budget must be positive.' }).optional()
    ),
    budgetMax: z.preprocess(
      (val) => (val === "" || val === null || val === undefined ? undefined : parseFloat(String(val))),
      z.number({ invalid_type_error: 'Max budget must be a number.' }).positive({ message: 'Max budget must be positive.' }).optional()
    ),
  }).optional()
}).refine(
  (data) => {
    if (data.preferences && typeof data.preferences.budgetMin === 'number' && typeof data.preferences.budgetMax === 'number') {
      return data.preferences.budgetMax >= data.preferences.budgetMin;
    }
    return true;
  },
  {
    message: "Max budget must be greater than or equal to min budget.",
    path: ["preferences", "budgetMax"],
  }
);

export type ProfileFormData = z.infer<typeof profileSchema>;



interface ProfileFormProps {
  control: Control<ProfileFormData>;
  errors: FieldErrors<ProfileFormData>;
  showDatePicker: (field: 'birthday' | 'anniversary') => void;
  birthdayValue: Date | null | undefined;
  anniversaryValue: Date | null | undefined;
  isNewProfile?: boolean; // Added isNewProfile prop
  profileId?: string; // Add profileId prop
}

const ProfileForm: React.FC<ProfileFormProps> = ({
  control,
  errors,
  showDatePicker,
  birthdayValue,
  anniversaryValue,
  isNewProfile, // Destructure the new prop
  profileId, // Destructure profileId
}) => {
  return (
    <View className="gap-4">
      {/* Basic Info should always be open */}
      <Accordion title="Basic Information" initialOpen={true} icon={<Feather color={"#A3002B"} name="user" size={20} className="mr-1 " />}>
        <BasicInfoSection
          control={control}
          errors={errors}
          showDatePicker={showDatePicker}
          birthdayValue={birthdayValue}
          anniversaryValue={anniversaryValue}
        />
      </Accordion>

      {/* Other sections collapsed by default for new profiles */}
      <Accordion title="Interests & Dislikes" initialOpen={!isNewProfile} icon={<Feather color={"#A3002B"} name="heart" size={20} className="mr-1 " />}>
        <InterestsDislikesSection
          control={control}
          errors={errors}
        />
      </Accordion>

      <Accordion title="Preferences" initialOpen={!isNewProfile} icon={<Feather color={"#A3002B"} name="sliders" size={20} className="mr-1 " />}>
        <PreferencesSection
          control={control}
          errors={errors}
        />
      </Accordion>

      <Accordion title="Sizes" initialOpen={!isNewProfile} icon={<Feather color={"#A3002B"} name="tag" size={20} className="mr-1 " />}>
        <SizesSection
          control={control}
          errors={errors}
        />
      </Accordion>

      <Accordion title="Wishlist" initialOpen={!isNewProfile} icon={<Feather color={"#A3002B"} name="star" size={20} className="mr-1 " />}>
        <WishlistSection
          control={control}
          errors={errors}
        />
      </Accordion>

      <Accordion title="Past Gifts" initialOpen={!isNewProfile} icon={<Feather color={"#A3002B"} name="archive" size={20} className="mr-1 " />}>
        <PastGiftsSection
          control={control}
          errors={errors}
        />
      </Accordion>

      <Accordion title="General Notes" initialOpen={!isNewProfile} icon={<Feather color={"#A3002B"} name="file-text" size={20} className="mr-1 " />}>
        <GeneralNotesSection
          control={control}
          errors={errors}
        />
      </Accordion>

      <Accordion title="Custom Important Dates" initialOpen={!isNewProfile} icon={<Feather color={"#A3002B"} name="calendar" size={20} className="mr-1 " />}>
        <CustomDatesSection
          control={control}
          errors={errors}
          profileId={profileId || null} // Pass profileId (or null if undefined)
        />
      </Accordion>
    </View>
  );
};

export default ProfileForm;