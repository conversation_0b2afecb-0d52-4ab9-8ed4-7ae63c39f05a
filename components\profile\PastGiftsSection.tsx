import React, { useState } from 'react';
import { View } from 'react-native';
import { Control, Controller, FieldErrors } from 'react-hook-form';
import { Timestamp } from 'firebase/firestore';
import { PastGiftGiven } from '../../types/firestore'; // Use client-side PastGiftGiven
import { PastGiftFormInput } from '@/components/profile/PastGiftFormInput';
import Button from '@/components/ui/Button';
import AddPastGiftModal from './AddPastGiftModal';
import { ProfileFormData } from './ProfileForm'; // Assuming ProfileFormData is exported from ProfileForm

interface PastGiftsSectionProps {
  control: Control<ProfileFormData>;
  errors: FieldErrors<ProfileFormData>;
}

// Helper function for removing past gifts
const handleRemovePastGift = (
  indexToRemove: number,
  // Update onChange and currentItems types to match react-hook-form data structure
  onChange: (value: Array<{ item: string; occasion?: string | undefined; date?: Date | null | undefined; reaction?: string | undefined; }>) => void,
  currentItems: Array<{ item: string; occasion?: string | undefined; date?: Date | null | undefined; reaction?: string | undefined; }>
) => {
  const updatedItems = currentItems.filter((_, index) => index !== indexToRemove);
  onChange(updatedItems);
};

const PastGiftsSection: React.FC<PastGiftsSectionProps> = ({
  control,
  errors,
}) => {
  const [isPastGiftModalVisible, setIsPastGiftModalVisible] = useState(false);

  return (
    <View className="gap-3">
      <Controller
        control={control}
        name="pastGiftsGiven"
        render={({ field: { onChange, value } }) => {
          const currentItems = value || [];
          // Handler for adding a new past gift
          const handleAddNewPastGift = (newItemData: Omit<PastGiftGiven, 'date'> & { date: Date | null }) => {
            const itemWithDate: PastGiftGiven = {
              ...newItemData,
              date: newItemData.date ? Timestamp.fromDate(newItemData.date) : null, // Convert Date to Timestamp
            };
            onChange([...currentItems, itemWithDate]);
          };

          return (
            <>
              {currentItems.map((giftData, index) => {
                // Convert giftData from form type to PastGiftGiven type for the input component
                const gift: PastGiftGiven = {
                  ...giftData,
                  // Convert Date | null | undefined from form to Timestamp | null for the prop
                  date: giftData.date instanceof Date ? Timestamp.fromDate(giftData.date) : null,
                };
                return (
                  <PastGiftFormInput
                    key={index}
                    gift={gift}
                    index={index}
                    onRemove={() => handleRemovePastGift(index, onChange, currentItems)}
                  />
                );
              })}
              <Button
                onPress={() => setIsPastGiftModalVisible(true)}
                title="Add Past Gift"
                accessibilityLabel="Add past gift button"
              />

              <AddPastGiftModal
                isVisible={isPastGiftModalVisible}
                onClose={() => setIsPastGiftModalVisible(false)}
                onAddItem={(newItemData: Omit<PastGiftGiven, 'date'> & { date: Date | null }) => {
                  handleAddNewPastGift(newItemData);
                  setIsPastGiftModalVisible(false);
                }}
              />
            </>
          );
        }}
      />
    </View>
  );
};

export default PastGiftsSection;