# Giftmi To-Do List (Detailed Breakdown)

*This list provides a more granular breakdown of tasks based on the revised architecture plan.*

## Phase 0: Project Initialization & Setup

*   **Environment Setup:**
    *   [x] Verify Node.js, npm/yarn, and Expo CLI versions are compatible.
    *   [x] Clone repository (if applicable) or initialize Git (`git init`).
    *   [x] Run `npm install` to install dependencies listed in `package.json`.
    *   [x] Run `expo start` and ensure the basic app loads on simulator/device/web.
*   **Firebase Setup:**
    *   [x] Create Firebase project via Firebase Console.
    *   [x] Register iOS and Android apps within the Firebase project.
    *   [x] Download configuration files (`GoogleService-Info.plist`, `google-services.json`) and place them correctly (Expo handles some of this automatically, verify).
    *   [x] Enable Firebase Authentication (Email/Password method initially).
    *   [x] Enable Firestore database and select region/mode.
    *   [ ] Enable Firebase Cloud Functions.
    *   [ ] Enable Firebase Storage (optional, if needed later).
*   **Local Development Setup:**
    *   [ ] Set up Firebase Emulators (Auth, Firestore, Functions) for local development and testing.
    *   [ ] Configure scripts in `package.json` to run emulators easily.
    *   [x] Set up Prettier and ESLint with project configurations (`.prettierrc.js`, `.eslintrc.js`).
    *   [ ] Configure editor integrations for Prettier/ESLint.
*   **Initial Documentation:**
    *   [x] Create/Review `README.md` with setup instructions.
    *   [x] Populate `memorybank/coding_standards.md` and `memorybank/style_guide.md` (as done previously).
    *   [x] Review `memorybank/tech_stack.md` and `memorybank/project_brief.md`.
*   **CI/CD Basics:**
    *   [x] Set up basic CI pipeline (e.g., GitHub Actions) to run Lint and potentially basic tests on push/PR.

## Phase 1: Core UI Shell & Authentication

*   [x] **Navigation Setup (Expo Router):**
    *   [x] Define main layout structure (`app/_layout.tsx`).
    *   [x] Create route groups for Auth (`app/(auth)/`) and Main App (`app/(app)/`).
    *   [x] Implement logic in root layout to redirect based on auth state.
*   [x] **Reusable UI Components (`components/ui/`):**
    *   [x] Create `Button` component (variants: primary, secondary, destructive).
    *   [x] Create `Input` component (variants: text, password, email).
    *   [x] Create `Card` component for consistent content wrapping.
    *   [x] Create `LoadingIndicator` component.
    *   [x] Create basic `Header` component.
*   [x] **Authentication Screens (`app/(auth)/`):**
    *   [x] Build `login.tsx` screen UI.
    *   [x] Build `signup.tsx` screen UI.
*   [x] **Authentication Logic (`contexts/AuthContext.tsx`):**
    *   [x] Create `AuthContext` to manage user state and tokens.
    *   [x] Implement `signUp` function (call Firebase Auth `createUserWithEmailAndPassword`).
    *   [x] Implement `signIn` function (call Firebase Auth `signInWithEmailAndPassword`).
    *   [x] Implement `signOut` function (call Firebase Auth `signOut`).
    *   [x] Implement logic to persist auth state (Expo SecureStore or AsyncStorage).
    *   [x] Connect Login/Signup screens to AuthContext functions.
    *   [x] Handle loading states and display errors during auth operations.
*   **Firestore Security Rules (Auth):**
    *   [x] Define basic rules allowing user profile creation/read/write only for authenticated users matching the UID.

## Phase 2: Significant Other Profile Management

*   **Firestore Schema & Types:**
    *   [x] Finalize Firestore schema for `users` and `significant_others` collections based on `architecture_plan.md`.
    *   [x] Define corresponding TypeScript types/interfaces in `types/firestore.ts`.
*   **Profile Screens (`app/(app)/profile/`):**
    *   [x] Create screen to list existing SO profiles (`index.tsx`).
    *   [x] Create screen to add a new SO profile (`add.tsx`).
    *   [x] Create screen to view/edit an existing SO profile (`[profileId].tsx`).
*   **Profile Forms:**
    *   [x] Build form components for adding/editing SO details (Name, Relationship, Dates, Interests, Preferences, Sizes, Dislikes, Notes, Wishlist, Past Gifts). Use reusable `Input` components.
    *   [x] Implement form validation (client-side initially).
*   **Firestore CRUD Operations (`services/profileService.ts` or similar):**
    *   [x] Implement function `addSignificantOther(userId, profileData)`.
    *   [x] Implement function `getSignificantOthers(userId)` (with pagination).
    *   [x] Implement function `getSignificantOtherById(userId, profileId)`.
    *   [x] Implement function `updateSignificantOther(userId, profileId, updatedData)`.
    *   [x] Implement function `deleteSignificantOther(userId, profileId)`.
*   **UI Integration:**
    *   [x] Connect profile screens to Firestore service functions.
    *   [x] Implement logic to display profile lists and details.
    *   [x] Handle loading and error states for Firestore operations.
*   **Firestore Security Rules (Profiles):**
    *   [x] Refine rules to ensure users can only CRUD their *own* `significant_others` documents.
    *   [x] Add validation rules for data types and required fields within security rules.
*   **Pagination:**
    *   [ ] Implement basic pagination logic for fetching the list of SO profiles if it could grow large.

## Phase 3: AI Integration - Setup & First Call

*   **Cloud Functions Setup:**
    *   [x] Initialize Firebase Functions (`firebase init functions`), select TypeScript.
    *   [x] Configure `functions/tsconfig.json` and `functions/package.json`.
    *   [x] Set up local Functions emulator workflow.
*   **LLM Provider & API Key:**
    *   [x] Decide on initial LLM provider (e.g., OpenAI).
    *   [x] Obtain API key from the provider.
    *   [x] Configure the API key securely using Firebase Functions environment variables (`firebase functions:config:set provider.key="YOUR_KEY"`). Access via `functions.config().provider.key`. Consider Google Secret Manager for enhanced security later.
*   **`getGiftRecommendations` Function (V1):**
    *   [x] Create `functions/src/index.ts` and define the HTTP-callable function `getGiftRecommendations`.
    *   [x] Implement request validation (ensure necessary parameters like `profileId` are present).
    *   [x] Implement Firebase Auth context validation (ensure user is authenticated).
    *   [x] Install LLM provider's SDK (`npm install openai --prefix functions`).
    *   [x] Implement logic to fetch the specified SO profile data from Firestore using the validated `userId` and `profileId`.
    *   [x] Implement basic error handling if profile fetch fails.
*   **Monitoring/Logging (Initial):**
    *   [x] Add basic logging (`functions.logger.info`, `.error`) within the function for key steps and errors.
*   **Gift Ideas Screen (`app/(app)/recommendations/`):**
    *   [x] Create basic UI structure for displaying gift recommendations (`[profileId].tsx`).
    *   [x] Add a button or trigger to fetch recommendations.
*   **API Call from App:**
    *   [x] Implement a service function (`services/recommendationService.ts`) to call the `getGiftRecommendations` Cloud Function (using Firebase Functions callable SDK or HTTPS).
    *   [x] Connect the Gift Ideas screen trigger to this service function.
    *   [x] Handle loading state while fetching.

## Phase 4: AI Integration - Prompting & Display

*   **Prompt Engineering (V1):**
    *   [x] Define the initial structure and content for the prompt sent to the LLM within the Cloud Function. Include key profile details (interests, preferences, dislikes, occasion context if available).
    *   [x] Specify the desired output format in the prompt (e.g., request JSON).
*   **LLM API Call (Function):**
    *   [x] Implement the actual call to the LLM API using the SDK and the securely stored API key.
    *   [] Add basic retry logic for transient network errors or API rate limits (e.g., exponential backoff).
    *   [x] Implement error handling for API-specific errors (invalid key, quota exceeded, etc.). Log errors.
*   **Response Parsing (Function):**
    *   [x] Implement logic to parse the LLM response (expecting JSON).
    *   [x] Handle cases where the response format is unexpected or invalid.
    *   [x] Format the parsed recommendations into a consistent structure to return to the app.
*   **Display Recommendations (App):**
    *   [x] Update the Gift Ideas screen to receive and display the formatted recommendations from the Cloud Function.
    *   [x] Handle potential errors returned from the function call.
*   **Prompt Evaluation Metrics (Initial):**
    *   [] Define how V1 prompts will be evaluated (e.g., manual review of relevance, user feedback later).

## Home Screen Rework

*   **Profile Fetching & Display:**
    *   [x] Implement logic to fetch and display SO profiles on home screen (`app/(app)/home.tsx`)
    *   [x] Create conditional UI for when no profiles exist vs. when profiles are available
*   **Profile Selection:**
    *   [x] Implement profile selection UI (dropdown/text) on home screen
*   **Recommendation Integration:**
    *   [x] Integrate recommendation fetching and display directly into home screen
    *   [x] Handle loading/error/empty states for both profiles and recommendations
*   **UI Consolidation:**
    *   [x] Remove dedicated recommendations screen functionality (now handled in home screen)
    *   [x] Update navigation to reflect consolidated flow

## Phase 5: AI Integration - Feedback Loop & Quality

*   **User Feedback UI:**
    *   [x] Add UI elements (e.g., thumbs up/down, flag button) to recommendation items on the home screen (`app/(app)/home.tsx`).
*   **Store Feedback Data:**
    *   [x] Define Firestore structure (e.g., a subcollection under `significant_others` or a separate `recommendation_feedback` collection) to store user feedback (recommendation ID, rating, flag, timestamp, userId, profileId).
    *   [x] Implement Firestore service function to save feedback.
    *   [x] Connect UI feedback elements to the save function.
*   **Feedback Loop Logic (V1 - Function):**
    *   [x] Modify `getGiftRecommendations` function (or a related process) to fetch recent negative feedback for the profile.
    *   [x] Implement logic to either:
        *   Filter LLM results post-call to remove items similar to negatively flagged ones.
        *   OR slightly adjust the prompt to de-prioritize topics related to negative feedback.
*   **Fallback Strategy (Function):**
    *   [ ] Implement logic within `getGiftRecommendations` to return a fallback response (e.g., generic suggestions based on interests, or a specific error message) if the LLM call fails critically or returns unusable content after retries.

## Phase 6: Supporting Features & UX Refinements

*   **Date Tracking & Notifications:**
    *   [ ] Ensure Birthday/Anniversary fields use Firestore Timestamp type.
    *   [ ] Create a screen (`app/(app)/calendar/` or similar) to display upcoming dates based on SO profiles.
    *   [ ] Set up Firebase Cloud Messaging (FCM) in the Expo app (request permissions).
    *   [ ] Create a scheduled Cloud Function (e.g., runs daily via Cloud Scheduler).
    *   [ ] Implement logic in the scheduled function to query upcoming dates from Firestore.
    *   [ ] Implement logic to trigger FCM push notifications for relevant users.
    *   [ ] Handle notification display/interaction in the app.
*   **UX Refinements:**
    *   [ ] Design and implement the "Cold Start" experience for users with new SO profiles (e.g., guided questions, initial generic tips).
    *   [ ] Refine the SO profile form for progressive data entry (e.g., start simple, prompt for more details later).
    *   [ ] Finalize recommendation presentation format (include images? fetch links?).
    *   [ ] Implement smooth animations for transitions and interactions using Reanimated.
    *   [ ] Add haptic feedback for key interactions.

## Phase 7: Operational Readiness & Polish

*   **Testing:**
    *   [ ] Write unit tests (Jest) for utility functions and complex component logic.
    *   [ ] Write integration tests (Jest/RNTL/Firebase Emulators) for:
        *   Auth flow.
        *   Profile CRUD operations.
        *   Cloud Function logic (fetching data, calling mocked LLM, parsing response).
        *   Recommendation display and feedback saving.
*   **Monitoring & Alerting:**
    *   [ ] Set up Google Cloud Monitoring dashboards for key Function metrics (invocations, latency, errors).
    *   [ ] Configure Google Cloud Logging for structured logs from Functions.
    *   [ ] Set up Billing Alerts for Firebase/Google Cloud usage (especially LLM API costs).
    *   [ ] Set up basic error alerting (e.g., Cloud Monitoring alerts on high function error rates).
*   **CI/CD Pipeline:**
    *   [ ] Enhance CI pipeline to run all tests (unit, integration).
    *   [ ] Set up CD pipeline (e.g., EAS Build/Submit) for deploying builds to TestFlight/Internal Testing and eventually production.
*   **Error Handling:**
    *   [ ] Conduct thorough review of error handling across the app and backend functions. Ensure user-facing errors are helpful.
*   **Documentation:**
    *   [ ] Populate `style_guide.md` and `coding_standards.md` fully.
    *   [ ] Ensure `README.md` is comprehensive for setup.
    *   [ ] Keep all `memorybank` documents updated.
*   **Final Polish:**
    *   [ ] Address any remaining UI/UX inconsistencies.
    *   [ ] Perform thorough testing on multiple devices/platforms.
    *   [ ] Optimize performance (bundle size, runtime performance).