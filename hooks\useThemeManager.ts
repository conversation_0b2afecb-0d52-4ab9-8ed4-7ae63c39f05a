import { useColorScheme } from 'nativewind';
import { useState, useEffect, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

const THEME_STORAGE_KEY = '@Giftmi:themePreference';

type ThemePreference = 'light' | 'dark' | 'system' | null;

export const useThemeManager = () => {
  const { colorScheme, setColorScheme } = useColorScheme();
  const [userPreference, setUserPreference] = useState<ThemePreference>(null);

  useEffect(() => {
    const loadThemePreference = async () => {
      try {
        const storedPreference = await AsyncStorage.getItem(THEME_STORAGE_KEY);
        if (storedPreference) {
          setColorScheme(storedPreference as 'light' | 'dark' | 'system');
          setUserPreference(storedPreference as ThemePreference);
        } else {
          // Default to system if no preference is stored
          setColorScheme('system');
          setUserPreference('system');
        }
      } catch (error) {
        console.error('Failed to load theme preference from AsyncStorage', error);
        // Fallback to system if loading fails
        setColorScheme('system');
        setUserPreference('system');
      }
    };

    loadThemePreference();
  }, [setColorScheme]); // Add setColorScheme to dependency array

  const updateThemePreference = useCallback(async (newPreference: 'light' | 'dark' | 'system') => {
    try {
      await AsyncStorage.setItem(THEME_STORAGE_KEY, newPreference);
      setColorScheme(newPreference);
      setUserPreference(newPreference);
    } catch (error) {
      console.error('Failed to save theme preference to AsyncStorage', error);
    }
  }, [setColorScheme]); // Add setColorScheme to dependency array

  return {
    colorScheme, // The currently active scheme (system, light, or dark)
    userPreference, // The user's explicitly set preference (light, dark, or system, or null initially)
    updateThemePreference,
  };
};