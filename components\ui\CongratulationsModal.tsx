import React, { useState, useEffect, useCallback } from 'react';
import {
  Modal,
  View,
  Text,
  Image,
  TouchableOpacity,
  LayoutChangeEvent, // HIGH 4: Correct type for layout event
  StyleSheet, // For local styles
  Dimensions, // For responsive sizing
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withDelay,
  withSequence,
  withSpring,
  Easing,
  interpolateColor,
  // HIGH 5: Removed unused Extrapolation, interpolate
} from 'react-native-reanimated';
import Button from './Button'; // Assuming Button is a standard, non-animated component
// import { colors } from '../../constants/Colors'; // MEDIUM 1: Assuming for theme colors

// --- Animation & Style Constants ---
const POMEGRANATE_MAX_SIZE = Dimensions.get('window').width * 0.5; // CRITICAL 1: Responsive max size
const SEED_IMAGE_SIZE = POMEGRANATE_MAX_SIZE * 0.35; // Relative to pomegranate
const GLOW_SIZE_FACTOR = 0.8; // Glow size relative to pomegranate

const ANIM_DURATION_NORMAL = 600;
const ANIM_DURATION_FAST = 200;
const ANIM_DURATION_IMPACT = 150;
const ANIM_DELAY_SEQUENCE = 100;

const INITIAL_ELEMENT_OPACITY = 0;
const INITIAL_ELEMENT_SCALE = 0.9;
const INITIAL_TEXT_TRANSLATE_Y = 20;
const INITIAL_SEED_POSITION_Y = -SEED_IMAGE_SIZE * 0.75; // Start off-screen

// --- Color Placeholders (Replace with your theme) ---
// MEDIUM 1: Abstracted colors
const colorTheme = {
  modalBackdrop: 'rgba(0,0,0,0.5)',
  cardBackgroundLight: '#FFFFFF', // Example light theme card
  cardBackgroundDark: '#1A1A1A', // Example dark theme card
  textPrimaryLight: '#000000',
  textPrimaryDark: '#FFFFFF',
  textSecondaryLight: '#555555',
  textSecondaryDark: '#AAAAAA',
  buttonInitialBg: '#A0A0A0', // Placeholder for disabled/initial button
  buttonFinalBg: '#007AFF', // Placeholder for primary action button (e.g., Giftmi Blue)
  glowEffect: '#FF6B6B', // Placeholder for glow
  // ... add other colors from your Giftmi style guide
};
// For simplicity in this example, not implementing full dark mode switching logic here,
// but colors are structured to support it.

interface PomegranateLayoutType {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface CongratulationsModalProps {
  isVisible: boolean;
  onContinue: () => void;
  profileName?: string; // HIGH 2: Will be used
}

const CongratulationsModal: React.FC<CongratulationsModalProps> = ({
  isVisible,
  onContinue,
  profileName,
}) => {
  // Animation shared values
  const seedPositionY = useSharedValue(INITIAL_SEED_POSITION_Y);
  const seedOpacity = useSharedValue(INITIAL_ELEMENT_OPACITY);
  const seedRotation = useSharedValue(0);
  const seedScale = useSharedValue(1);

  const pomegranateScale = useSharedValue(1);
  const pomegranateRotation = useSharedValue(0);

  const glowOpacity = useSharedValue(INITIAL_ELEMENT_OPACITY);
  const glowScale = useSharedValue(0.5); // Initial scale for glow

  const textOpacity = useSharedValue(INITIAL_ELEMENT_OPACITY);
  const textTranslateY = useSharedValue(INITIAL_TEXT_TRANSLATE_Y);

  const buttonOpacity = useSharedValue(INITIAL_ELEMENT_OPACITY);
  const buttonScale = useSharedValue(INITIAL_ELEMENT_SCALE);
  const buttonAnimationProgress = useSharedValue(0);

  const [pomegranateLayout, setPomegranateLayout] =
    useState<PomegranateLayoutType | null>(null);
  const [actualPomegranateSize, setActualPomegranateSize] = useState({
    width: 0,
    height: 0,
  });

  // --- Helper Functions for Animation Logic ---
  // MEDIUM 5: Refactoring useEffect into smaller functions
  const resetAllAnimations = useCallback(() => {
    // HIGH 1: Complete reset of all relevant shared values
    seedPositionY.value = INITIAL_SEED_POSITION_Y;
    seedOpacity.value = INITIAL_ELEMENT_OPACITY;
    seedRotation.value = 0;
    seedScale.value = 1; // Reset to default scale

    pomegranateScale.value = 1; // Reset to default scale
    pomegranateRotation.value = 0;

    glowOpacity.value = INITIAL_ELEMENT_OPACITY;
    glowScale.value = 0.5; // Reset to initial glow scale

    textOpacity.value = INITIAL_ELEMENT_OPACITY;
    textTranslateY.value = INITIAL_TEXT_TRANSLATE_Y;

    buttonOpacity.value = INITIAL_ELEMENT_OPACITY;
    buttonScale.value = INITIAL_ELEMENT_SCALE;
    buttonAnimationProgress.value = 0;
  }, [
    seedPositionY,
    seedOpacity,
    seedRotation,
    seedScale,
    pomegranateScale,
    pomegranateRotation,
    glowOpacity,
    glowScale,
    textOpacity,
    textTranslateY,
    buttonOpacity,
    buttonScale,
    buttonAnimationProgress,
  ]);

  const startIntroAnimations = useCallback(() => {
    textOpacity.value = withDelay(
      ANIM_DELAY_SEQUENCE,
      withTiming(1, { duration: ANIM_DURATION_NORMAL })
    );
    textTranslateY.value = withDelay(
      ANIM_DELAY_SEQUENCE,
      withTiming(0, { duration: ANIM_DURATION_NORMAL })
    );

    pomegranateScale.value = withSequence(
      withTiming(1.05, {
        duration: ANIM_DURATION_NORMAL * 1.5,
        easing: Easing.inOut(Easing.sin),
      }),
      withTiming(0.95, {
        duration: ANIM_DURATION_NORMAL * 1.5,
        easing: Easing.inOut(Easing.sin),
      }),
      withTiming(1, {
        duration: ANIM_DURATION_NORMAL * 1.5,
        easing: Easing.inOut(Easing.sin),
      })
    );
  }, [textOpacity, textTranslateY, pomegranateScale]);

  const startMainSequenceAnimations = useCallback(
    (currentPomegranateLayout: PomegranateLayoutType) => {
      // HIGH 6: Corrected targetSeedY to land seed center on pomegranate center (approx)
      const targetSeedY =
        currentPomegranateLayout.y +
        currentPomegranateLayout.height / 2 -
        SEED_IMAGE_SIZE / 2;
      const preImpactSeedY = targetSeedY - 10; // For the slight overshoot before spring

      seedOpacity.value = withTiming(1, { duration: ANIM_DURATION_NORMAL });
      seedRotation.value = withSequence(
        withTiming(-15, { duration: ANIM_DURATION_FAST }),
        withTiming(15, { duration: ANIM_DURATION_FAST * 2 }),
        withTiming(-10, { duration: ANIM_DURATION_FAST * 1.5 }),
        withTiming(5, { duration: ANIM_DURATION_FAST * 1.25 }),
        withTiming(0, { duration: ANIM_DURATION_FAST })
      );

      seedPositionY.value = withDelay(
        ANIM_DELAY_SEQUENCE * 3, // Delay seed drop slightly
        withTiming(
          preImpactSeedY,
          {
            duration: ANIM_DURATION_NORMAL * 0.9,
            easing: Easing.inOut(Easing.quad),
          },
          (finished) => {
            if (finished) {
              seedPositionY.value = withSpring(targetSeedY, {
                damping: 6,
                stiffness: 100,
                mass: 0.5,
              });

              pomegranateScale.value = withSequence(
                withTiming(1.15, {
                  duration: ANIM_DURATION_IMPACT,
                  easing: Easing.out(Easing.quad),
                }),
                withTiming(0.95, {
                  duration: ANIM_DURATION_IMPACT,
                  easing: Easing.in(Easing.quad),
                }),
                withTiming(1.05, {
                  duration: ANIM_DURATION_IMPACT,
                  easing: Easing.out(Easing.quad),
                }),
                withTiming(1, {
                  duration: ANIM_DURATION_IMPACT,
                  easing: Easing.in(Easing.quad),
                })
              );
              pomegranateRotation.value = withSequence(
                withTiming(-2, { duration: ANIM_DURATION_IMPACT / 1.5 }),
                withTiming(2, { duration: ANIM_DURATION_IMPACT * 1.3 }),
                withTiming(0, { duration: ANIM_DURATION_IMPACT / 1.5 })
              );

              glowOpacity.value = withSequence(
                withTiming(0.7, { duration: ANIM_DURATION_FAST }),
                withTiming(0, { duration: ANIM_DURATION_NORMAL * 1.5 })
              );
              glowScale.value = withSequence(
                withTiming(0.8, { duration: ANIM_DURATION_FAST }),
                withTiming(1.5, { duration: ANIM_DURATION_NORMAL * 1.5 })
              );

              const buttonDelay = ANIM_DELAY_SEQUENCE * 2;
              buttonOpacity.value = withDelay(
                buttonDelay,
                withTiming(1, { duration: ANIM_DURATION_FAST * 2 })
              );
              buttonScale.value = withDelay(
                buttonDelay,
                withSpring(1, { damping: 12, stiffness: 100 })
              );
              buttonAnimationProgress.value = withDelay(
                buttonDelay,
                withTiming(1, { duration: ANIM_DURATION_NORMAL * 0.8 })
              );

              seedScale.value = withDelay(
                ANIM_DELAY_SEQUENCE,
                withTiming(0.8, { duration: ANIM_DURATION_FAST * 1.5 })
              );
              seedOpacity.value = withDelay(
                ANIM_DELAY_SEQUENCE * 1.5,
                withTiming(0, { duration: ANIM_DURATION_NORMAL * 0.8 })
              );
            }
          }
        )
      );
    },
    [
      seedPositionY,
      seedOpacity,
      seedRotation,
      seedScale,
      pomegranateScale,
      pomegranateRotation,
      glowOpacity,
      glowScale,
      buttonOpacity,
      buttonScale,
      buttonAnimationProgress,
    ]
  );

  useEffect(() => {
    if (isVisible) {
      resetAllAnimations();
      startIntroAnimations();
      if (pomegranateLayout) {
        // MEDIUM 4: Animation depends on layout
        startMainSequenceAnimations(pomegranateLayout);
      }
    } else {
      resetAllAnimations(); // Ensure reset when modal is hidden externally
    }
  }, [
    isVisible,
    pomegranateLayout,
    resetAllAnimations,
    startIntroAnimations,
    startMainSequenceAnimations,
  ]);

  // --- Layout Measurement ---
  const onPomegranateLayout = useCallback((event: LayoutChangeEvent) => {
    // HIGH 4
    const { x, y, width, height } = event.nativeEvent.layout;
    setPomegranateLayout({ x, y, width, height });
    setActualPomegranateSize({ width, height }); // Store actual rendered size
  }, []);

  // --- Animated Styles ---
  const seedAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateY: seedPositionY.value },
      { rotate: `${seedRotation.value}deg` },
      { scale: seedScale.value },
    ],
    opacity: seedOpacity.value,
  }));

  const pomegranateAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: pomegranateScale.value },
      { rotate: `${pomegranateRotation.value}deg` },
    ],
    // CRITICAL 1: Style with responsive max size
    width: POMEGRANATE_MAX_SIZE,
    height: POMEGRANATE_MAX_SIZE,
  }));

  const glowAnimatedStyle = useAnimatedStyle(() => {
    // CRITICAL 2: Dynamic glow positioning and sizing
    const dynamicGlowSize = actualPomegranateSize.width * GLOW_SIZE_FACTOR; // Size relative to pomegranate
    return {
      opacity: glowOpacity.value,
      transform: [{ scale: glowScale.value }],
      width: dynamicGlowSize,
      height: dynamicGlowSize,
      borderRadius: dynamicGlowSize / 2,
      // Position dynamically behind the pomegranate center
      // This assumes pomegranateLayout gives coordinates relative to its direct parent,
      // and the glow is a sibling placed absolutely.
      top: pomegranateLayout
        ? pomegranateLayout.y +
          actualPomegranateSize.height / 2 -
          dynamicGlowSize / 2
        : -10000, // Hide if no layout
      left: pomegranateLayout
        ? pomegranateLayout.x +
          actualPomegranateSize.width / 2 -
          dynamicGlowSize / 2
        : -10000, // Hide if no layout
    };
  });

  const textContainerAnimatedStyle = useAnimatedStyle(() => ({
    // Renamed for clarity
    opacity: textOpacity.value,
    transform: [{ translateY: textTranslateY.value }],
  }));

  const buttonWrapperAnimatedStyle = useAnimatedStyle(() => {
    // Renamed for clarity
    const backgroundColor = interpolateColor(
      // MEDIUM 1: Use themed colors
      buttonAnimationProgress.value,
      [0, 1],
      [colorTheme.buttonInitialBg, colorTheme.buttonFinalBg]
    );
    return {
      backgroundColor,
      opacity: buttonOpacity.value,
      transform: [{ scale: buttonScale.value }],
    };
  });

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={isVisible}
      onRequestClose={onContinue}
      // MEDIUM 2: Removed empty className
    >
      <TouchableOpacity
        style={[
          styles.modalBackdrop,
          { backgroundColor: colorTheme.modalBackdrop },
        ]}
        activeOpacity={1}
        onPress={onContinue} // Allow closing by tapping backdrop
      >
        {/* Use View for content instead of directly on TouchableOpacity for better layout control */}
        <View
          style={[
            styles.modalContent,
            { backgroundColor: colorTheme.cardBackgroundLight },
          ]}
        >
          {/* Ensure this View is the one handling centering and padding for its children */}

          {/* Glow effect container - CRITICAL 2 */}
          {/* The glow is now absolutely positioned relative to the pomegranate's measured layout */}
          <Animated.View
            style={[
              styles.glowBase, // Base styles for glow (zIndex, position)
              glowAnimatedStyle, // Dynamic size, position, animation
              { backgroundColor: colorTheme.glowEffect }, // MEDIUM 1
            ]}
          />

          {/* Pomegranate Image (positioned by its parent's flex layout) */}
          <Animated.Image
            onLayout={onPomegranateLayout}
            source={require('../../assets/images/pomegranate.png')} // CRITICAL 1: Ensure this asset is optimized
            style={pomegranateAnimatedStyle} // Includes responsive width/height
            resizeMode="contain"
          />

          {/* Seed Image (Animated) */}
          {pomegranateLayout && ( // Only render if pomegranate has laid out
            <Animated.Image
              source={require('../../assets/images/seed.png')} // Ensure this asset is optimized
              style={[
                styles.seedBase, // Base styles for seed
                seedAnimatedStyle,
                { width: SEED_IMAGE_SIZE, height: SEED_IMAGE_SIZE },
                // Recalculate seed's absolute left based on container, if pomegranate isn't full width
                // For simplicity, assuming pomegranate parent handles centering of pomegranate image.
                // Seed's `left: 50%` and `marginLeft` will center it within its parent.
                // The `top: 0` in original audit means it starts at top of its parent.
                // This is overridden by `translateY`.
              ]}
              resizeMode="contain"
            />
          )}

          {/* Text Content */}
          <Animated.View
            style={[textContainerAnimatedStyle, styles.textContainer]}
          >
            {/* LOW 1: Styled text, HIGH 2: Used profileName */}
            <Text
              style={[styles.titleText, { color: colorTheme.textPrimaryLight }]}
            >
              Congratulations{profileName ? `, ${profileName}` : ''}!
            </Text>
            <Text
              style={[
                styles.subtitleText,
                { color: colorTheme.textSecondaryLight },
              ]}
            >
              You've successfully set up a new profile.
            </Text>
          </Animated.View>

          {/* Button */}
          {/* HIGH 3: Apply animated style only to wrapper */}
          <Animated.View
            style={[buttonWrapperAnimatedStyle, styles.buttonWrapper]}
          >
            <Button
              // `style` prop on custom Button is for *internal static* styles, not Reanimated styles
              // className="rounded-full" // Assuming Button component handles its own styling via className or props
              title="Continue"
              onPress={onContinue}
              // Pass any theme props to Button if it supports them
            />
          </Animated.View>
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

// --- Styles ---
// LOW 1: Example of centralizing some styles
const styles = StyleSheet.create({
  modalBackdrop: {
    flex: 1,
    justifyContent: 'center', // Center modal content vertically
    alignItems: 'center', // Center modal content horizontally
  },
  modalContent: {
    position: 'relative', // Important for absolute positioning of children like glow and seed
    alignItems: 'center', // Center children like pomegranate, text, button
    padding: 20,
    borderRadius: 16,
    width: '90%', // Or a fixed max width
    maxWidth: 400,
    elevation: 5, // Android shadow
    shadowColor: '#000', // iOS shadow
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  glowBase: {
    position: 'absolute',
    zIndex: 0, // Behind pomegranate
  },
  pomegranateContainer: {
    // If needed for more complex layout around pomegranate
    position: 'relative', // For glow to be positioned within
    alignItems: 'center',
    justifyContent: 'center',
    // marginBottom: 20, // Space for seed to "fall into" if visual requires
  },
  seedBase: {
    position: 'absolute',
    // left: '50%', // Centering handled by parent (modalContent)
    // marginLeft: -SEED_IMAGE_SIZE / 2, // Centering handled by parent
    // top: 0, // Initial vertical position controlled by translateY
    zIndex: 1, // Above pomegranate, below text/button if needed
  },
  textContainer: {
    alignItems: 'center',
    marginTop: 20, // Spacing after pomegranate/seed animation area
    marginBottom: 25,
  },
  titleText: {
    fontSize: 22,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitleText: {
    fontSize: 16,
    textAlign: 'center',
  },
  buttonWrapper: {
    width: '100%', // Button wrapper takes full width of modal content padding
    borderRadius: 50, // For the animated background color to be rounded
    overflow: 'hidden', // Ensure background color respects border radius
  },
});

export default CongratulationsModal;
