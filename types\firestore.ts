import { Timestamp } from 'firebase/firestore';

export interface UserSettings {
  userId: string; // Or implicitly the document ID
  reminders?: {
    birthdays?: boolean;
    anniversaries?: boolean;
    holidays?: boolean;
    customDates?: boolean;
  };
  // Add other settings like budget later
  updatedAt?: Timestamp;
}

export interface SOPreferences {
  favoriteColor?: string;
  preferredStyle?: string;
  favoriteBrands?: string[]; // Assuming this might exist or be added
  budgetMin?: number;
  budgetMax?: number;
  // ... other preference fields
}

// Client-side version of WishlistItem
export interface WishlistItem {
  item: string;
  notes?: string;
  link?: string;
  dateAdded: Timestamp | null;
}

// Client-side version of PastGiftGiven
export interface PastGiftGiven {
  item: string;
  occasion?: string;
  date: Timestamp | null;
  reaction?: string;
}

// Client-side version of GeneralNote
export interface GeneralNote {
  note: string;
  date: Timestamp | null;
}

// Client-side version of CustomDate
export interface CustomDate {
  id: string;
  name: string;
  date: Timestamp | null;
  customDateMonthDay?: string; // "MM-DD" format
}

// Client-side version of SignificantOtherProfile
export interface SignificantOtherProfile {
  userId: string;
  profileId: string;
  name: string;
  relationship: string;
  birthday?: Timestamp | null;
  anniversary?: Timestamp | null;
  birthdayMonthDay?: string;
  anniversaryMonthDay?: string;
  interests: string[];
  dislikes: string[];
  preferences?: SOPreferences & { // Combine with existing and allow others
    favoriteColor?: string | null;
    preferredStyle?: string | null;
    favoriteBrands?: string[];
    budgetMin?: number;
    budgetMax?: number;
    [key: string]: any; // Allow other preference fields
  };
  sizes?: {
    clothing?: string | null;
    shoe?: string | null;
    [key: string]: any; // Allow other size categories
  };
  wishlistItems: WishlistItem[];
  pastGiftsGiven: PastGiftGiven[];
  generalNotes: GeneralNote[];
  customDates?: CustomDate[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
  // Add any other top-level fields planned for client-side use
}

// Add other Firestore related types here as needed