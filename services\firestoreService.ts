import {
  collection,
  doc,
  setDoc,
  serverTimestamp,
  FirestoreError,
  FieldValue, // Import FieldValue
  query, // Added
  where, // Added
  getDocs, // Added
  orderBy, // Added
  getDoc, // Import getDoc
  updateDoc, // Add updateDoc import
  deleteDoc, // Import deleteDoc
  Timestamp, // Import Timestamp for date conversion
  writeBatch, // Import writeBatch for batch operations
} from 'firebase/firestore';
import { db } from '../firebaseConfig'; // Import the initialized Firestore instance
import { SignificantOtherProfile } from '../functions/src/types/firestore';
import { UserSettings } from '../types/firestore';

// Type for the data structure when writing to Firestore, allowing FieldValue for timestamps
type SignificantOtherProfileWriteData = Omit<SignificantOtherProfile, 'createdAt' | 'updatedAt'> & {
  createdAt: FieldValue;
  updatedAt: FieldValue;
};

// Define the expected input shape for profile data from the form
export interface AddProfileData {
  name: string;
  relationship: string;
  interests?: string[]; // Now accepts arrays
  dislikes?: string[];  // Now accepts arrays
  favoriteColor?: string; // Add new fields
  preferredStyle?: string;
  clothingSize?: string;
  shoeSize?: string;
  favoriteBrands?: string; // Added favoriteBrands
  // Add other fields as they are added to the form
  birthday?: Date | null; // Optional birthday field
  anniversary?: Date | null; // Optional anniversary field
}

/**
 * Adds a new significant other profile document to Firestore.
 *
 * @param userId - The ID of the user creating the profile.
 * @param profileData - The basic profile data (name, relationship) from the form.
 * @returns The ID of the newly created profile document.
 * @throws Throws an error if the Firestore operation fails.
 */
export const addSignificantOther = async (
  userId: string,
  profileData: AddProfileData
): Promise<string> => {
  if (!userId) {
    throw new Error('User ID is required to add a significant other profile.');
  }
  if (!profileData || !profileData.name || !profileData.relationship) {
    throw new Error('Profile data (name and relationship) is required.');
  }

  try {
    // Get a reference to the 'significant_others' collection
    const profilesCollectionRef = collection(db, 'significant_others');

    // Create a new document reference with an auto-generated ID
    const newProfileRef = doc(profilesCollectionRef);
    const profileId = newProfileRef.id;

    // Construct the full document data using the write-specific type
    const fullDocumentData: SignificantOtherProfileWriteData = {
      userId: userId,
      profileId: profileId,
      name: profileData.name,
      relationship: profileData.relationship,
      // Convert birthday and anniversary to Firestore Timestamps if provided
      birthday: profileData.birthday ? Timestamp.fromDate(profileData.birthday) : undefined,
      anniversary: profileData.anniversary ? Timestamp.fromDate(profileData.anniversary) : undefined,
      interests: profileData.interests ?? [],
      dislikes: profileData.dislikes ?? [],
      preferences: {
        favoriteColor: profileData.favoriteColor ?? undefined,
        preferredStyle: profileData.preferredStyle ?? undefined,
        // Initialize other preference fields later if needed
      },
      sizes: {
        clothing: profileData.clothingSize ?? undefined,
        shoe: profileData.shoeSize ?? undefined,
        // Initialize other size fields later if needed
      },
      wishlistItems: [],
      pastGiftsGiven: [],
      generalNotes: [],
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    };

    // Use setDoc to save the document with the generated ID
    await setDoc(newProfileRef, fullDocumentData);

    console.log(`Successfully added profile with ID: ${profileId} for user: ${userId}`);
    return profileId; // Return the new profile ID
  } catch (error) {
    const firestoreError = error as FirestoreError;
    console.error(
      'Error adding significant other profile to Firestore:',
      firestoreError.code,
      firestoreError.message
    );
    // Re-throw a more specific error or handle it as needed
    throw new Error(`Failed to add significant other profile: ${firestoreError.message}`);
  }
};

/**
 * Fetches all significant other profiles created by a specific user.
 *
 * @param userId - The ID of the user whose profiles to fetch.
 * @returns A promise that resolves to an array of SignificantOtherProfile objects.
 *          Returns an empty array if no profiles are found or if an error occurs.
 */
export const getSignificantOthers = async (userId: string): Promise<SignificantOtherProfile[]> => {
  if (!userId) {
    // console.error('User ID is required to fetch significant others.');
    // return []; // Return empty array if no userId is provided
    throw new Error('User ID is required to fetch significant others.');
  }

  try {
    // Get a reference to the 'significant_others' collection
    const profilesCollectionRef = collection(db, 'significant_others');

    // Create a query to find profiles matching the userId, ordered by creation date descending
    const q = query(
      profilesCollectionRef,
      where('userId', '==', userId),
      orderBy('createdAt', 'desc') // Order by creation timestamp, newest first
    );

    // Execute the query
    const querySnapshot = await getDocs(q);

    // Map the documents to SignificantOtherProfile objects
    const profiles: SignificantOtherProfile[] = querySnapshot.docs.map((doc) => {
      // Combine document data with the document ID (as profileId)
      // Cast to SignificantOtherProfile, assuming data matches the interface
      // Note: Firestore Timestamps will be included here. Handle conversion in the UI if needed.
      return {
        profileId: doc.id,
        ...(doc.data() as Omit<SignificantOtherProfile, 'profileId'>), // Spread the rest of the data
      };
    });

    console.log(`Fetched ${profiles.length} profiles for user: ${userId}`);
    return profiles;
  } catch (error) {
    const firestoreError = error as FirestoreError;
    console.error(
      'Error fetching significant other profiles from Firestore:',
      firestoreError.code,
      firestoreError.message
    );
    // Return an empty array in case of error to prevent UI crashes
    // return [];
    throw new Error(`Failed to fetch significant other profiles: ${firestoreError.message}`);
  }
};

/**
 * Fetches a single significant other profile by its document ID.
 *
 * @param profileId - The ID of the profile document to fetch.
 * @returns A promise that resolves to the SignificantOtherProfile object if found, otherwise null.
 */
export const getSignificantOtherById = async (
  profileId: string
): Promise<SignificantOtherProfile | null> => {
  if (!profileId) {
    // console.error('Profile ID is required to fetch a significant other.');
    // return null;
    throw new Error('Profile ID is required to fetch a significant other.');
  }

  try {
    const docRef = doc(db, 'significant_others', profileId);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      // Combine document data with the document ID
      const profileData = {
        profileId: docSnap.id,
        ...(docSnap.data() as Omit<SignificantOtherProfile, 'profileId'>),
      };
      console.log(`Fetched profile with ID: ${profileId}`);
      return profileData;
    } else {
      console.log(`No profile found with ID: ${profileId}`);
      return null;
    }
  } catch (error) {
    const firestoreError = error as FirestoreError;
    console.error(
      `Error fetching significant other profile with ID ${profileId}:`,
      firestoreError.code,
      firestoreError.message
    );
    // return null; // Return null in case of error
    throw new Error(`Failed to fetch significant other profile with ID ${profileId}: ${firestoreError.message}`);
  }
};

/**
 * Updates an existing significant other profile document in Firestore.
 *
 * @param profileId - The ID of the profile document to update.
 * @param updatedData - An object containing the fields to update. Use Partial<SignificantOtherProfile>.
 * @returns A promise that resolves when the update is successful.
 * @throws Throws an error if the Firestore operation fails.
 */
export const updateSignificantOther = async (
  profileId: string,
  updatedData: Partial<SignificantOtherProfile>
): Promise<void> => {
  if (!profileId) {
    throw new Error('Profile ID is required to update a significant other profile.');
  }
  if (!updatedData || Object.keys(updatedData).length === 0) {
    console.warn('No update data provided for profile:', profileId);
    return; // Nothing to update
  }

  try {
    // Get a reference to the specific document
    const profileDocRef = doc(db, 'significant_others', profileId);

    // Prepare the data payload, ensuring updatedAt is always set
    // Destructure to remove immutable fields and collect the rest
    const {
      profileId: _pId, // Destructure to remove, assign to throwaway var
      userId: _uId,     // Destructure to remove
      createdAt: _cA,   // Destructure to remove
      ...restOfData    // Collect remaining properties
    } = updatedData;

    const dataToUpdatePayload = {
      ...restOfData, // Spread only the properties intended for update
      updatedAt: serverTimestamp(),
    };

    // Update the document
    await updateDoc(profileDocRef, dataToUpdatePayload);

    console.log(`Successfully updated profile with ID: ${profileId}`);
  } catch (error) {
    const firestoreError = error as FirestoreError;
    console.error(
      `Error updating significant other profile with ID ${profileId}:`,
      firestoreError.code,
      firestoreError.message
    );
    // Re-throw a more specific error or handle it as needed
    throw new Error(`Failed to update significant other profile: ${firestoreError.message}`);
  }
};

/**
 * Deletes a significant other profile document from Firestore.
 *
 * @param profileId - The ID of the profile document to delete.
 * @returns A promise that resolves when the deletion is successful.
 * @throws Throws an error if the Firestore operation fails.
 */
export const deleteSignificantOther = async (profileId: string): Promise<void> => {
  if (!profileId) {
    throw new Error('Profile ID is required to delete a significant other profile.');
  }

  try {
    // Get a reference to the specific document
    const profileDocRef = doc(db, 'significant_others', profileId);

    // Delete the document
    await deleteDoc(profileDocRef);

    console.log(`Successfully deleted profile with ID: ${profileId}`);
  } catch (error) {
    const firestoreError = error as FirestoreError;
    console.error(
      `Error deleting significant other profile with ID ${profileId}:`,
      firestoreError.code,
      firestoreError.message
    );
    // Re-throw a more specific error or handle it as needed
    throw new Error(`Failed to delete significant other profile: ${firestoreError.message}`);
  }
};

/**
 * Saves a user's Expo push token to Firestore.
 * Handles adding new tokens and cleaning up old ones for the same user.
 *
 * @param userId - The ID of the authenticated user.
 * @param token - The Expo push token string.
 * @param platform - The platform the token was generated on (e.g., 'ios', 'android').
 * @throws Throws an error if the Firestore operation fails.
 */
export const savePushToken = async (
  userId: string,
  token: string,
  platform: string
): Promise<void> => {
  if (!userId) {
    // console.error('User ID is required to save a push token.');
    // return;
    throw new Error('User ID is required to save a push token.');
  }
  if (!token) {
    // console.error('Push token is required to save.');
    // return;
    throw new Error('Push token is required to save.');
  }
  if (!platform) {
    // console.error('Platform is required to save a push token.');
    // return;
    throw new Error('Platform is required to save a push token.');
  }

  try {
    const pushTokensCollectionRef = collection(db, 'pushTokens');
    // Query for this specific token for this user
    const q = query(
      pushTokensCollectionRef,
      where('userId', '==', userId),
      where('token', '==', token) // Check if this token already exists for the user
    );
    const existingTokenSnapshot = await getDocs(q);

    if (existingTokenSnapshot.empty) {
      // Token doesn't exist, add it
      console.log(`Adding new token ${token} for user ${userId}.`);
      const newTokenRef = doc(pushTokensCollectionRef); // Auto-generate ID for new token doc
      await setDoc(newTokenRef, {
        userId: userId,
        token: token,
        platform: platform,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });
    } else {
      // Token exists, optionally update its timestamp
      console.log(`Token ${token} already exists for user ${userId}. Updating timestamp.`);
      const tokenDocRef = existingTokenSnapshot.docs[0].ref;
      await updateDoc(tokenDocRef, { updatedAt: serverTimestamp() });
    }
    console.log(`Push token save/update process completed for user: ${userId}`);
  } catch (error) {
    const firestoreError = error as FirestoreError;
    console.error(
      'Error saving push token to Firestore:',
      firestoreError.code,
      firestoreError.message
    );
    throw new Error(`Failed to save push token: ${firestoreError.message}`);
  }
};

/**
 * Fetches user settings from Firestore.
 *
 * @param userId - The ID of the user whose settings to fetch.
 * @returns A promise that resolves to the UserSettings object if found, otherwise null.
 */
export const getUserSettings = async (userId: string): Promise<UserSettings | null> => {
  if (!userId) {
    // console.error('User ID is required to fetch user settings.');
    // return null;
    throw new Error('User ID is required to fetch user settings.');
  }

  try {
    const docRef = doc(db, 'userSettings', userId);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      console.log(`Fetched settings for user: ${userId}`);
      return docSnap.data() as UserSettings;
    } else {
      console.log(`No settings found for user: ${userId}`);
      return null;
    }
  } catch (error) {
    const firestoreError = error as FirestoreError;
    console.error(
      `Error fetching user settings for user ${userId}:`,
      firestoreError.code,
      firestoreError.message
    );
    // return null; // Return null in case of error
    throw new Error(`Failed to fetch user settings for user ${userId}: ${firestoreError.message}`);
  }
};

/**
 * Updates user settings in Firestore.
 *
 * Note: When using `setDoc` with `merge: true`, if `settingsData` contains nested objects
 * (like `reminders`), the entire existing nested object in Firestore will be replaced
 * by the one provided in `settingsData`. It does not perform a deep merge of nested fields.
 *
 * @param userId - The ID of the user whose settings to update.
 * @param settingsData - An object where keys are dot-notated paths to the fields to update (e.g., "reminders.birthdays")
 *                       and values are the new values for those fields. Must also include `updatedAt`.
 * @returns A promise that resolves when the update is successful.
 * @throws Throws an error if the Firestore operation fails.
 */
export const updateUserSettings = async (
  userId: string,
  settingsData: { [key: string]: any } // Accepts an object with dot notation for granular updates
): Promise<void> => {
  if (!userId) {
    throw new Error('User ID is required to update user settings.');
  }
  // Ensure settingsData includes updatedAt, or add it if not present and only specific fields are being updated.
  // For simplicity, the caller (handleSettingChange) will now ensure updatedAt is part of settingsData.
  if (!settingsData || Object.keys(settingsData).length === 0) {
    console.warn('No update data provided for user settings:', userId);
    return; // Nothing to update
  }
  if (!settingsData.updatedAt) {
    // This check is a safeguard, but handleSettingChange should provide it.
    console.warn('updateUserSettings called without updatedAt field in settingsData.');
    settingsData.updatedAt = serverTimestamp();
  }


  try {
    const docRef = doc(db, 'userSettings', userId);

    // Use updateDoc for granular updates with dot notation
    await updateDoc(docRef, settingsData);

    console.log(`Successfully updated settings for user: ${userId}`);
  } catch (error) {
    const firestoreError = error as FirestoreError;
    console.error(
      `Error updating user settings for user ${userId}:`,
      firestoreError.code,
      firestoreError.message
    );
    throw new Error(`Failed to update user settings: ${firestoreError.message}`);
  }
};