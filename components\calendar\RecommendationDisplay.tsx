import React from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import Animated, { FadeIn, FadeOut, SlideInUp } from 'react-native-reanimated';
import { Feather } from '@expo/vector-icons';
import resolveConfig from 'tailwindcss/resolveConfig';
import tailwindConfig from '../../tailwind.config'; // Adjust path as needed

// Resolve Tailwind config to access theme values
const fullConfig = resolveConfig(tailwindConfig);
const themeColors = fullConfig.theme?.colors || {};

// Helper to safely access nested theme colors
const getThemeColor = (path: string, fallback: string): string => {
  const keys = path.split('.');
  let color = themeColors as any;
  for (const key of keys) {
    if (color && typeof color === 'object' && key in color) {
      color = color[key];
    } else {
      return fallback; // Return fallback if path is invalid
    }
  }
  // Handle cases where the resolved value might be an object (e.g., primary: { 500: ... })
  if (typeof color === 'object' && 'DEFAULT' in color) {
    return color.DEFAULT;
  }
  if (typeof color === 'string') {
    return color;
  }
  return fallback; // Return fallback if final value is not a string
};


interface RecommendationDisplayProps {
  recommendations: any[]; // Define a more specific type if possible
  isLoading: boolean;
  error: string | null;
}

const RecommendationDisplay: React.FC<RecommendationDisplayProps> = ({ recommendations, isLoading, error }) => {
  if (isLoading) {
    return (
      <Animated.View 
        entering={FadeIn.duration(300)} 
        exiting={FadeOut.duration(200)} 
        className="items-center justify-center py-6 mt-4 border shadow-sm bg-card dark:bg-card-dark rounded-xl border-border/30 dark:border-border-dark"
      >
        <ActivityIndicator size="large" color={getThemeColor('primary.500', '#E87900')} />
        <Text className="mt-3 text-base text-text-secondary dark:text-text-secondary-dark">Generating gift ideas...</Text>
      </Animated.View>
    );
  }

  if (error) {
    return (
      <Animated.View 
        entering={FadeIn.duration(300)} 
        exiting={FadeOut.duration(200)} 
        className="items-center py-6 mt-4 border shadow-sm bg-card dark:bg-card-dark rounded-xl border-border/30 dark:border-border-dark"
      >
        <Feather name="alert-circle" size={24} color={getThemeColor('error', '#EF4444')} />
        <Text className="px-4 mt-2 text-base text-center text-error">{error}</Text>
      </Animated.View>
    );
  }

  if (recommendations.length === 0) {
    return null; // Don't render anything if no recommendations and not loading/error
  }

  return (
    <Animated.View 
      entering={SlideInUp.duration(400)} 
      exiting={FadeOut.duration(300)} 
      className="p-4 mt-6 border shadow-sm bg-card dark:bg-card-dark rounded-xl border-border/30 dark:border-border-dark"
    >
      <View className="flex-row items-center mb-3">
        <Feather name="gift" size={20} color={getThemeColor('primary.500', '#E87900')} />
        <Text className="ml-2 text-lg font-semibold text-text-primary dark:text-text-primary-dark">Gift Ideas</Text>
      </View>
      <View>
        {recommendations.map((item, index) => (
          <View 
            key={index} 
            className={`py-3 ${index < recommendations.length - 1 ? 'border-b border-border/30' : ''}`}
          >
            <Text className="text-base font-medium text-text-primary dark:text-text-primary-dark">{item.name}</Text>
            {item.description && (
              <Text className="mt-1.5 text-sm text-text-secondary dark:text-text-secondary-dark">{item.description}</Text>
            )}
            <View className="flex-row flex-wrap mt-2">
              {item.priceRange && (
                <View className="flex-row items-center mb-1 mr-4">
                  <Feather name="tag" size={14} color={getThemeColor('primary.600', '#CC6A00')} />
                  <Text className="ml-1 text-xs font-medium text-primary-600">{item.priceRange}</Text>
                </View>
              )}
              {item.categories && item.categories.length > 0 && (
                <View className="flex-row items-center mb-1">
                  <Feather name="list" size={14} color={getThemeColor('accent.600', '#0F766E')} />
                  <Text className="ml-1 text-xs font-medium text-accent-600">
                    {item.categories.join(', ')}
                  </Text>
                </View>
              )}
            </View>
          </View>
        ))}
      </View>
    </Animated.View>
  );
};

export default RecommendationDisplay;