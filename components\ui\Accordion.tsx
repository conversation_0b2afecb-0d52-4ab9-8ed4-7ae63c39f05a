import React, { useState } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons';

interface AccordionProps {
  title: string;
  children: React.ReactNode;
  initialOpen?: boolean;
  icon?: React.ReactElement;
}

const Accordion: React.FC<AccordionProps> = ({
  title,
  children,
  initialOpen = false,
  icon
}) => {
  const [isOpen, setIsOpen] = useState(initialOpen);

  return (
    <View className="overflow-hidden border rounded-lg border-border">
      <TouchableOpacity
        className="flex-row items-center justify-between p-4"
        onPress={() => setIsOpen(!isOpen)}
        accessibilityRole="button"
        accessibilityState={{ expanded: isOpen }}
      >
        <View className="flex-row items-center">
          {icon && <View className="mr-2">{icon}</View>}
          <Text className="text-base font-medium text-primary-500">{title}</Text>
        </View>
        <Feather
          name={isOpen ? 'chevron-up' : 'chevron-down'}
          size={20}
          color="black"
        />
      </TouchableOpacity>

      {isOpen ? <View className="p-4">{children}</View> : null}
    </View>
  );
};

export default Accordion;