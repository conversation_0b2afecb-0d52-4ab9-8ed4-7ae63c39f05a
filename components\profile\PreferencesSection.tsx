import React from 'react';
import { View, Text } from 'react-native';
import { Control, Controller, FieldErrors } from 'react-hook-form';
import Dropdown from '@/components/ui/Dropdown';
import ColorSwatchDropdown from '@/components/ui/ColorSwatchDropdown';
import { TagInput } from '@/components/ui/TagInput';
import Input from '@/components/ui/Input'; // Import the Input component
import { brandSuggestions } from '@/constants/suggestions';
import { colorOptions, styleOptions } from '@/constants/profileOptions'; // Import options from new file
import { ProfileFormData } from './ProfileForm'; // Assuming ProfileFormData is exported from ProfileForm

interface PreferencesSectionProps {
  control: Control<ProfileFormData>;
  errors: FieldErrors<ProfileFormData>;
}

const PreferencesSection: React.FC<PreferencesSectionProps> = ({
  control,
  errors,
}) => {
  return (
    <View className="gap-3">
      {/* Favorite Color */}
      <View>
        <Text className="mb-1 text-sm font-medium text-muted-foreground">Favorite Color</Text>
        <Controller
          control={control}
          name="preferences.favoriteColor" // Updated name
          render={({ field: { onChange, value } }) => (
            <ColorSwatchDropdown
              options={colorOptions}
              selectedValue={value}
              onValueChange={onChange}
              placeholder="Select Color"
              error={errors.preferences?.favoriteColor?.message} // Updated error path
              accessibilityLabel="Select favorite color dropdown"
            />
          )}
        />
      </View>
      {/* Preferred Style */}
      <View>
        <Text className="mb-1 text-sm font-medium text-muted-foreground">Preferred Style</Text>
        <Controller
          control={control}
          name="preferences.preferredStyle" // Updated name
          render={({ field: { onChange, value } }) => (
            <Dropdown
              options={styleOptions}
              selectedValue={value}
              onValueChange={onChange}
              placeholder="Select Style"
              error={errors.preferences?.preferredStyle?.message} // Updated error path
              accessibilityLabel="Select preferred style dropdown"
            />
          )}
        />
      </View>
      {/* Favorite Brands */}
      <View>
        <Text className="mb-1 text-sm font-medium text-muted-foreground">Favorite Brands</Text>
        <Controller
          control={control}
          name="preferences.favoriteBrands" // Updated name
          render={({ field: { onChange, value } }) => (
            <TagInput
              tags={value || []} // TagInput expects string[], value is string[] | undefined
              onChangeTags={onChange} // onChange expects string[]
              placeholder="e.g., Nike, Apple, Moleskine"
              error={errors.preferences?.favoriteBrands?.message} // Updated error path
              accessibilityLabel="Favorite brands input"
              suggestions={brandSuggestions}
            />
          )}
        />
      </View>
      {/* Minimum Budget */}
      <View>
        <Text className="mb-1 text-sm font-medium text-muted-foreground">Minimum Budget (Optional)</Text>
        <Controller
          control={control}
          name="preferences.budgetMin"
          render={({ field: { onChange, value } }) => (
            <Input
              placeholder="e.g., 50"
              keyboardType="numeric"
              onChangeText={onChange}
              value={value?.toString()} // Input expects string value
              error={errors.preferences?.budgetMin?.message}
              accessibilityLabel="Minimum budget input"
            />
          )}
        />
      </View>
      {/* Maximum Budget */}
      <View>
        <Text className="mb-1 text-sm font-medium text-muted-foreground">Maximum Budget (Optional)</Text>
        <Controller
          control={control}
          name="preferences.budgetMax"
          render={({ field: { onChange, value } }) => (
            <Input
              placeholder="e.g., 200"
              keyboardType="numeric"
              onChangeText={onChange}
              value={value?.toString()} // Input expects string value
              error={errors.preferences?.budgetMax?.message}
              accessibilityLabel="Maximum budget input"
            />
          )}
        />
      </View>
    </View>
  );
};

export default PreferencesSection;