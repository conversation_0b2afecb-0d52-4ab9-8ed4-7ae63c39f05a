import React, { useState } from 'react';
import { View } from 'react-native';
import { Control, Controller, FieldErrors } from 'react-hook-form';
import { Timestamp } from 'firebase/firestore';
import { GeneralNote } from '../../types/firestore'; // Use client-side GeneralNote
import { GeneralNoteFormInput } from '@/components/profile/GeneralNoteFormInput';
import Button from '@/components/ui/Button';
import { AddGeneralNoteModal } from './AddGeneralNoteModal';
import { ProfileFormData } from './ProfileForm'; // Assuming ProfileFormData is exported from ProfileForm

interface GeneralNotesSectionProps {
  control: Control<ProfileFormData>;
  errors: FieldErrors<ProfileFormData>;
}

// Helper function for removing general notes
const handleRemoveGeneralNote = (
  indexToRemove: number,
  // Update onChange and currentItems types to match react-hook-form data structure
  onChange: (value: Array<{ note: string; date?: Date | null | undefined }>) => void,
  currentItems: Array<{ note: string; date?: Date | null | undefined }>
) => {
  const updatedItems = currentItems.filter((_, index) => index !== indexToRemove);
  onChange(updatedItems);
};

const GeneralNotesSection: React.FC<GeneralNotesSectionProps> = ({
  control,
  errors,
}) => {
  const [isGeneralNoteModalVisible, setIsGeneralNoteModalVisible] = useState(false);

  return (
    <View className="gap-3">
      <Controller
        control={control}
        name="generalNotes"
        render={({ field }) => {
          const currentItems = field.value || [];
          // Handler for adding a new general note
          // newItemData comes from the modal and only contains the note string
          const handleAddNewGeneralNote = (newItemData: { note: string }) => {
            // Create the item with the note and a new Timestamp
            const itemWithDate = {
              ...newItemData,
              date: Timestamp.now(), // Add current timestamp
            };
            // Add the new item to the current items and update the form state
            field.onChange([...currentItems, itemWithDate]);
          };

          return (
            <>
              {currentItems.map((noteData, index) => {
               {/* Convert noteData from form type to GeneralNote type for the input component */}
                const note: GeneralNote = {
                  ...noteData,
                  // Convert Date | null | undefined from form to Timestamp | null for the prop
                  date: noteData.date instanceof Date ? Timestamp.fromDate(noteData.date) : null,
                };
                return (
                  <GeneralNoteFormInput
                    key={index}
                    note={note}
                    index={index}
                    onRemove={() => handleRemoveGeneralNote(index, field.onChange, currentItems)}
                  />
                );
              })}
              <Button
                onPress={() => setIsGeneralNoteModalVisible(true)}
                title="Add General Note"
                accessibilityLabel="Add general note button"
              />

              <AddGeneralNoteModal
                isVisible={isGeneralNoteModalVisible}
                onClose={() => setIsGeneralNoteModalVisible(false)}
                onSave={async (noteText: string) => { // Change prop name to onSave and adjust signature
                  // Create the new item data with the note text
                  const newItemData: Omit<GeneralNote, 'date'> = { note: noteText };
                  handleAddNewGeneralNote(newItemData);
                  setIsGeneralNoteModalVisible(false);
                }}
              />
            </>
          );
        }}
      />
    </View>
  );
};

export default GeneralNotesSection;