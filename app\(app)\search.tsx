import React, { useState, useMemo } from 'react';
import { View, Text, ActivityIndicator, SafeAreaView, StatusBar, ScrollView } from 'react-native';
import { useSearchRecommendations } from '../../hooks/useSearchRecommendations'; // Assuming path is correct
// GiftRecommendation type should be imported if used by RecommendationDisplay or for strong typing here
// import { GiftRecommendation } from '../../services/recommendationService'; 
import Input from '../../components/ui/Input';
import CustomButton from '../../components/ui/Button'; // Renamed to Button for consistency if it's the same component
import RecommendationDisplay from '../../components/calendar/RecommendationDisplay'; // Assuming path
import { Feather } from '@expo/vector-icons';
import { useThemeManager } from '../../hooks/useThemeManager'; // Assumed theme hook

// --- Centralized AppColors (Placeholder - Integrate with your actual theme) ---
const AppColors = {
  primary: '#E87900',
  primaryDark: '#D96D00',
  primaryContent: '#FFFFFF', // For text/icons on primary background

  background: '#F3F4F6',
  backgroundDark: '#111827',
  
  inputBackground: '#FFFFFF', // Example
  inputBackgroundDark: '#1F2937', // Example

  textPrimary: '#1F2937',
  textPrimaryDark: '#F9FAFB',
  textSecondary: '#6B7280',
  textSecondaryDark: '#9CA3AF',
  
  error: '#EF4444',
  errorDark: '#F87171',
  errorBackground: '#FEE2E2', // Example: bg-error/10
  errorBackgroundDark: '#4A1313', // Example: bg-error-dark/10 (approx)

  gray100: '#F3F4F6',
  gray400: '#9CA3AF',
  gray800: '#1F2937',

  white: '#FFFFFF', // Absolute white
  // ... other necessary colors
};
// --- End AppColors ---

const SearchScreen = () => {
  const [query, setQuery] = useState('');
  const { colorScheme } = useThemeManager();
  const { recommendations, isGenerating, recommendationError, fetchRecommendations } = useSearchRecommendations();

  const isDark = colorScheme === 'dark';

  // MEDIUM 1: Use themedColors for consistent theming
  const themedColors = useMemo(() => ({
    primary: isDark ? AppColors.primaryDark : AppColors.primary,
    primaryContent: AppColors.primaryContent, // Assuming white text on primary for both modes
    background: isDark ? AppColors.backgroundDark : AppColors.background,
    inputBackground: isDark ? AppColors.inputBackgroundDark : AppColors.inputBackground,
    textPrimary: isDark ? AppColors.textPrimaryDark : AppColors.textPrimary,
    textSecondary: isDark ? AppColors.textSecondaryDark : AppColors.textSecondary,
    error: isDark ? AppColors.errorDark : AppColors.error,
    errorBackground: isDark ? AppColors.errorBackgroundDark : AppColors.errorBackground,
    placeholder: isDark ? AppColors.textSecondaryDark : AppColors.textSecondary,
    gray400: AppColors.gray400, // For specific neutral icons
    initialIconBackground: isDark ? AppColors.primaryDark+'33' : AppColors.primary+'1A', // Primary with opacity
    noResultsIconBackground: isDark ? AppColors.gray800 : AppColors.gray100,
  }), [isDark]);

  const handleSearch = () => {
    if (query.trim()) {
      fetchRecommendations(query.trim());
    }
  };

  const renderContent = () => {
    if (isGenerating) {
      return (
        <View className="items-center justify-center flex-1 pt-12">
          <ActivityIndicator size="large" color={themedColors.primary} />
          <Text className="mt-4 text-base font-medium" style={{ color: themedColors.textSecondary }}>
            Finding gifts...
          </Text>
        </View>
      );
    }

    if (recommendationError) {
      // LOW 3: User-friendly error message
      console.error("Search failed:", recommendationError); // Log detailed error for devs
      return (
        <View className="items-center justify-center flex-1 px-6 pt-12">
          <View className="items-center justify-center w-16 h-16 mb-4 rounded-full" style={{ backgroundColor: themedColors.errorBackground }}>
            <Feather name="alert-triangle" size={28} color={themedColors.error} />
          </View>
          <Text className="mb-2 text-lg font-medium text-center" style={{ color: themedColors.error }}>
            Search Failed
          </Text>
          <Text className="text-base text-center" style={{ color: themedColors.textSecondary }}>
            Sorry, we couldn't fetch results. Please check your connection and try again.
          </Text>
        </View>
      );
    }

    if (recommendations && recommendations.length > 0) {
      // MEDIUM 2: Pass actual recommendationError to RecommendationDisplay
      // LOW 4: isGenerating will be false here, so isLoading on RecommendationDisplay is effectively false.
      // This is fine if RecommendationDisplay doesn't have its own internal loading states independent of the main search.
      return (
        <RecommendationDisplay
          recommendations={recommendations}
          isLoading={isGenerating} // Will be false when this branch is taken
          error={recommendationError} // Pass error, though it's null in this branch
        />
      );
    }

    if (recommendations && recommendations.length === 0) {
      return (
        <View className="items-center justify-center flex-1 px-6 pt-12">
          <View className="items-center justify-center w-16 h-16 mb-4 rounded-full" style={{ backgroundColor: themedColors.noResultsIconBackground }}>
            <Feather name="search" size={28} color={themedColors.gray400} />
          </View>
          <Text className="mb-2 text-lg font-medium text-center" style={{ color: themedColors.textPrimary }}>
            No Results Found
          </Text>
          <Text className="text-base text-center" style={{ color: themedColors.textSecondary }}>
            Try broadening your search terms or checking for typos.
          </Text>
        </View>
      );
    }

    // Initial state (before any search or after clearing query)
    return (
      <View className="items-center justify-center flex-1 px-6 pt-12">
        <View className="items-center justify-center w-16 h-16 mb-4 rounded-full bg-primary-500/20">
          <Feather name="gift" size={28} color={"#A3002B"} />
        </View>
        <Text className="text-lg text-center" style={{ color: themedColors.textSecondary }}>
          Enter a query above to search for gift ideas.
        </Text>
      </View>
    );
  };

  return (
    <SafeAreaView className="flex-1 bg-background">
      <StatusBar barStyle={isDark ? 'light-content' : 'dark-content'} />
      
      {/* Wrapped content in ScrollView to ensure search bar is always visible if keyboard appears */}
      <ScrollView 
        contentContainerStyle={{ flexGrow: 1, paddingBottom: 20 }} 
        className="p-5" 
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled" // Good for usability with inputs
      >
        <Text className="mb-6 text-2xl font-bold text-primary-500" >
          Gift Search
        </Text>

        <View className="flex-row items-center gap-3">
          <View className="flex-1">
            <Input
              placeholder="Search for gifts, brands, ideas..."
              value={query}
              onChangeText={setQuery}
              keyboardType="default"
              autoCapitalize="none"
              returnKeyType="search"
              onSubmitEditing={handleSearch}
              // Assuming Input component handles its own theming or accepts style props
              // For direct styling if Input doesn't fully theme:
              // style={{
              //   backgroundColor: themedColors.inputBackground,
              //   borderColor: themedColors.border,
              //   color: themedColors.textPrimary,
              // }}
              // placeholderTextColor={themedColors.placeholder} // If Input supports this prop
              className="px-4 py-3 text-base border rounded-lg bg-input-background dark:bg-input-background-dark border-border dark:border-border-dark text-text-primary dark:text-text-primary-dark placeholder:text-text-secondary dark:placeholder:text-text-secondary-dark"
            />
          </View>
          
          {/* LOW 2: Assuming CustomButton uses NativeWind classes for theming primarily */}
          <CustomButton // Renamed to Button if it's your standard app button
            onPress={handleSearch}
            disabled={isGenerating || !query.trim()}
            title="Search"
            // className should ideally make it use themed primary colors via NativeWind config
            className="px-4 py-3 mb-4 rounded-lg bg-primary dark:bg-primary-dark" 
            textClassName="text-white font-medium text-base" // Assuming white text for primary button
            // LOW 1: Use themed color for icon
            leftIcon={<Feather name="search" size={18} color={themedColors.primaryContent} />}
          />
        </View>

        <View className="flex-1">
          {renderContent()}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default SearchScreen;