import React, { useState, useRef, useEffect, ReactNode } from 'react';
import {
  View,
  Text,
  Alert,
  ActivityIndicator,
  ScrollView,
  SafeAreaView,
  Modal,
  Pressable,
  TouchableOpacity,
  StyleProp,
  ViewStyle,
} from 'react-native';
import { useAuth } from '../../../contexts/AuthContext';
import {
  sendEmailVerification,
  sendPasswordResetEmail,
  getAuth,
  User,
  deleteUser,
  reauthenticateWithCredential,
  EmailAuthProvider,
} from 'firebase/auth';
import { getFunctions, httpsCallable } from 'firebase/functions';
import Input from '../../../components/ui/Input'; // Import Input component
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  FadeIn,
  FadeOut,
} from 'react-native-reanimated';
import { Feather } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

// --- Prop Types ---

interface AnimatedCardProps {
  children: ReactNode;
  className?: string;
  dangerZone?: boolean;
  style?: StyleProp<ViewStyle>; // Allow passing additional styles if needed
}

interface ButtonProps {
  title: string;
  onPress: () => void;
  disabled?: boolean;
  className?: string;
  // Use Feather icon names explicitly if possible, or string for flexibility
  iconName?: keyof typeof Feather.glyphMap | string;
  variant?: 'primary' | 'secondary' | 'error' | 'outline';
  isLoading?: boolean; // Add isLoading prop
}

interface StatusBadgeProps {
  verified: boolean;
}

interface IconBadgeProps {
  // Use Feather icon names explicitly if possible, or string for flexibility
  name: keyof typeof Feather.glyphMap | string;
  color: string;
  bgColor: string; // Tailwind class string
}

// --- Components ---

// Custom Animated Card component with subtle animation
const AnimatedCard: React.FC<AnimatedCardProps> = ({
  children,
  className = '',
  dangerZone = false,
  style,
}) => {
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
    };
  });

  const onPressIn = () => {
    scale.value = withSpring(0.98, { damping: 20, stiffness: 300 });
  };

  const onPressOut = () => {
    scale.value = withSpring(1, { damping: 20, stiffness: 300 });
  };

  return (
    <Pressable onPressIn={onPressIn} onPressOut={onPressOut}>
      <Animated.View
        entering={FadeIn.duration(400)}
        style={[animatedStyle, style]} // Combine animated style with potential passed styles
        className={`bg-card dark:bg-card-dark rounded-xl shadow-md ${
          dangerZone ? 'border-2 border-error dark:border-error-dark' : ''
        } ${className}`}
      >
        {children}
      </Animated.View>
    </Pressable>
  );
};

// Enhanced Button component with animation and haptic feedback
const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  disabled = false,
  className = '',
  iconName,
  variant = 'primary',
  isLoading = false,
}) => {
  const scale = useSharedValue(1);

  const getButtonStyle = () => {
    if (disabled || isLoading) return 'bg-disabled dark:bg-disabled-dark';

    switch (variant) {
      case 'error':
        return 'bg-error dark:bg-error-dark';
      case 'outline':
        return 'bg-transparent border border-primary dark:border-primary-dark';
      case 'secondary':
        return 'bg-accent dark:bg-accent-dark';
      default: // primary
        return 'bg-primary dark:bg-primary-dark';
    }
  };

  const getTextStyle = () => {
    if (disabled || isLoading) {
      return variant === 'outline'
        ? 'text-primary/50 dark:text-primary-dark/50'
        : 'text-white/50';
    }
    if (variant === 'outline') {
      return 'text-primary dark:text-primary-dark';
    }
    return 'text-white'; // Assuming primary, secondary, error text is white
  };

  const getIconColor = () => {
    if (disabled || isLoading) return '#A0A0A0'; // Example disabled color
    return variant === 'outline' ? '#E87900' : 'white';
  };

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
    };
  });

  const handlePress = () => {
    if (disabled || isLoading) return;

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    scale.value = withSpring(0.95, { damping: 10, stiffness: 300 });

    setTimeout(() => {
      scale.value = withSpring(1, { damping: 15, stiffness: 300 });
      if (onPress) onPress();
    }, 100);
  };

  return (
    <Animated.View style={animatedStyle}>
      <TouchableOpacity
        onPress={handlePress}
        disabled={disabled || isLoading} // Disable if loading
        activeOpacity={0.8}
        className={`px-4 py-3 rounded-lg flex-row items-center justify-center ${getButtonStyle()} ${className}`}
      >
        {isLoading ? (
          <ActivityIndicator
            size="small"
            color={getIconColor()}
            className="mr-2"
          />
        ) : (
          iconName && (
            <Feather
              name={iconName as keyof typeof Feather.glyphMap}
              size={18}
              color={getIconColor()}
              className="mr-2"
            />
          )
        )}
        <Text className={`font-medium text-base ${getTextStyle()}`}>
          {isLoading ? 'Loading...' : title}
        </Text>
      </TouchableOpacity>
    </Animated.View>
  );
};

// Status Badge component for verified/unverified status
const StatusBadge: React.FC<StatusBadgeProps> = ({ verified }) => (
  <View
    className={`flex-row items-center ${
      verified
        ? 'bg-success/10 dark:bg-success-dark/20'
        : 'bg-error/10 dark:bg-error-dark/20'
    } px-2 py-1 rounded-full`}
  >
    <Feather
      name={verified ? 'check-circle' : 'alert-circle'}
      size={14}
      color={verified ? '#22C55E' : '#EF4444'}
    />
    <Text
      className={`ml-1 font-medium text-xs ${
        verified
          ? 'text-success dark:text-success-dark'
          : 'text-error dark:text-error-dark'
      }`}
    >
      {verified ? 'Verified' : 'Not Verified'}
    </Text>
  </View>
);

// Icon Badge component for section headers
const IconBadge: React.FC<IconBadgeProps> = ({ name, color, bgColor }) => (
  <View
    className={`h-8 w-8 rounded-full ${bgColor} items-center justify-center`}
  >
    <Feather
      name={name as keyof typeof Feather.glyphMap}
      size={16}
      color={color}
    />
  </View>
);

// Helper to safely get error message
const getErrorMessage = (error: unknown): string => {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return 'An unknown error occurred';
};

const PersonalInfoScreen = () => {
  const { user, signOutUser } = useAuth(); // Assuming useAuth provides Firebase User object or similar structure
  const auth = getAuth();
  const currentUser: User | null = auth.currentUser; // Get the currently authenticated user

  const [isResendingVerification, setIsResendingVerification] = useState(false); // Specific loading state
  const [isResettingPassword, setIsResettingPassword] = useState(false); // Specific loading state
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false); // Keep for deletion process and overlay
  const [passwordForReauth, setPasswordForReauth] = useState(''); // State for password input
  const scrollViewRef = useRef<ScrollView>(null);
  const opacity = useSharedValue(0);

  const handleResendVerification = async () => {
    if (!currentUser) {
      Alert.alert('Error', 'No authenticated user found.');
      return;
    }

    setIsResendingVerification(true); // Use specific loading state
    try {
      await sendEmailVerification(currentUser);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      Alert.alert(
        'Success',
        'Verification email sent. Please check your inbox.'
      );
    } catch (error: any) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      let errorMessage = 'Failed to send verification email. Please try again.';
      if (error.code === 'auth/too-many-requests') {
        errorMessage = 'Too many requests. Please try again later.';
      } else if (error.code === 'auth/network-request-failed') {
        errorMessage = 'Network error. Please check your internet connection.';
      }
      Alert.alert('Error', errorMessage);
    } finally {
      setIsResendingVerification(false); // Reset specific loading state
    }
  };

  const handleResetPassword = async () => {
    if (!user?.email) {
      Alert.alert('Error', 'User email not found.');
      return;
    }
    const userEmail = user.email; // Capture email in a const

    Alert.alert(
      'Reset Password',
      `Send password reset email to ${userEmail}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Yes',
          onPress: async () => {
            setIsResettingPassword(true); // Use specific loading state
            try {
              await sendPasswordResetEmail(auth, userEmail);
              Haptics.notificationAsync(
                Haptics.NotificationFeedbackType.Success
              );
              Alert.alert(
                'Success',
                'Password reset email sent. Please check your inbox.'
              );
            } catch (error: any) {
              Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
              let errorMessage =
                'Failed to send password reset email. Please try again.';
              if (error.code === 'auth/user-not-found') {
                errorMessage =
                  'User not found. Please check the email address.';
              } else if (error.code === 'auth/too-many-requests') {
                errorMessage = 'Too many requests. Please try again later.';
              } else if (error.code === 'auth/network-request-failed') {
                errorMessage =
                  'Network error. Please check your internet connection.';
              }
              Alert.alert('Error', errorMessage);
            } finally {
              setIsResettingPassword(false); // Reset specific loading state
            }
          },
        },
      ]
    );
  };

  // Check if the user signed in using email/password
  const isPasswordProvider = user?.providerData.some(
    (provider) => provider.providerId === 'password'
  );

  const handleConfirmDelete = async () => {
    setIsDeleting(true);
    setIsDeleteModalVisible(false); // Close modal immediately

    const auth = getAuth();
    const currentUser = auth.currentUser;

    if (!currentUser) {
      setIsDeleting(false);
      Alert.alert('Error', 'No authenticated user found.');
      return;
    }

    try {
      // --- Re-authentication Step ---
      if (isPasswordProvider) {
        if (!passwordForReauth) {
          // This case should ideally be prevented by disabling the button
          // or showing an inline error, but adding a check here as a fallback.
          Alert.alert('Error', 'Please enter your password to confirm.');
          setIsDeleting(false);
          setIsDeleteModalVisible(true); // Re-open modal to allow password entry
          return;
        }

        console.log('Attempting re-authentication with password...');
        const credential = EmailAuthProvider.credential(
          currentUser.email!,
          passwordForReauth
        );
        await reauthenticateWithCredential(currentUser, credential);
        console.log('Re-authentication successful.');
      } else {
        // Handle re-authentication for other providers (e.g., Google, Facebook)
        // This requires triggering the provider's re-auth flow, which is more complex.
        // For now, show an informative message.
        Alert.alert(
          'Re-authentication Required',
          `Please re-authenticate with your ${
            currentUser.providerData[0]?.providerId || 'provider'
          } to delete your account. You may need to sign out and sign back in.`
        );
        setIsDeleting(false);
        return; // Stop the deletion process
      }
      // --- End Re-authentication Step ---

      // Call Cloud Function to delete user data
      const functions = getFunctions();
      const deleteUserDataFn = httpsCallable(functions, 'deleteUserData');
      console.log('Calling deleteUserData Cloud Function...');
      await deleteUserDataFn();
      console.log('deleteUserData Cloud Function succeeded.');

      // Delete Firebase Auth user
      console.log('Calling deleteUser Firebase Auth function...');
      await deleteUser(currentUser);
      console.log('deleteUser Firebase Auth function succeeded.');

      // Handle Success - only sign out if both steps succeeded
      console.log('Account deletion successful. Signing out...');
      // Assuming signOutUser handles navigation
      signOutUser();
    } catch (error: any) {
      console.error('Account deletion failed:', error);
      let errorMessage = 'Failed to delete account. Please try again.';

      // Provide more specific feedback for re-authentication errors
      if (error.code === 'auth/requires-recent-login') {
        errorMessage =
          'Please sign out and sign back in to delete your account.';
      } else if (
        error.code === 'auth/invalid-credential' ||
        error.code === 'auth/wrong-password'
      ) {
        errorMessage = 'Incorrect password. Please try again.';
        setIsDeleteModalVisible(true); // Re-open modal for password retry
      } else if (error.code === 'auth/user-not-found') {
        errorMessage = 'User not found. Please sign out and sign back in.';
      } else if (error.code === 'auth/network-request-failed') {
        errorMessage = 'Network error. Please check your internet connection.';
      } else if (error.code === 'functions/internal') {
        errorMessage =
          'An internal server error occurred. Please try again later.';
      }
      // Add more specific error handling for other potential errors from deleteUserDataFn or deleteUser

      Alert.alert('Deletion Failed', errorMessage);
    } finally {
      setIsDeleting(false);
      setPasswordForReauth(''); // Clear password input
    }
  };

  const animatedLoaderStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
      // Ensure loader is only interactive when visible
      pointerEvents: opacity.value > 0.5 ? 'auto' : ('none' as 'auto' | 'none'),
    };
  });

  useEffect(() => {
    // Use isDeleting for the overlay visibility
    opacity.value = withTiming(isDeleting ? 1 : 0, { duration: 300 });
  }, [isDeleting, opacity]); // Depend on isDeleting - only deletion shows the overlay

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
      <ScrollView
        ref={scrollViewRef}
        contentContainerStyle={{ padding: 16, paddingBottom: 32 }} // Use style for contentContainer
        showsVerticalScrollIndicator={false}
      >
        <Text className="mb-6 text-2xl font-bold text-text-primary dark:text-text-primary-dark">
          Account Settings
        </Text>

        <AnimatedCard className="p-5 mb-6">
          <View className="flex-row items-center justify-between mb-5">
            <Text className="text-xl font-semibold text-text-primary dark:text-text-primary-dark">
              Profile
            </Text>
            <IconBadge
              name="user"
              color="#E87900"
              bgColor="bg-primary/20 dark:bg-primary-dark/30"
            />
          </View>

          <View className="mb-4">
            <Text className="mb-1 text-sm text-text-secondary dark:text-text-secondary-dark">
              Email
            </Text>
            <Text className="text-base font-medium text-text-primary dark:text-text-primary-dark">
              {user?.email ?? 'N/A'}
            </Text>
          </View>

          <View className="flex-row items-center mb-5">
            <Text className="mr-2 text-sm text-text-secondary dark:text-text-secondary-dark">
              Verification Status:
            </Text>
            <StatusBadge verified={!!user?.emailVerified} />
          </View>

          {!user?.emailVerified && (
            <Button
              title="Resend Verification Email"
              iconName="mail"
              onPress={handleResendVerification}
              disabled={
                isResendingVerification || isResettingPassword || isDeleting
              } // Disable if any operation is in progress
              isLoading={isResendingVerification} // Pass specific loading state
              // className prop is optional now, no need to add empty one unless styling needed
            />
          )}
        </AnimatedCard>

        <AnimatedCard className="p-5 mb-6">
          <View className="flex-row items-center justify-between mb-5">
            <Text className="text-xl font-semibold text-text-primary dark:text-text-primary-dark">
              Security
            </Text>
            <IconBadge
              name="shield"
              color="#0D9488"
              bgColor="bg-accent/20 dark:bg-accent-dark/30"
            />
          </View>

          {isPasswordProvider && (
            <>
              <Text className="mb-4 text-sm text-text-secondary dark:text-text-secondary-dark">
                Update your password regularly to keep your account secure.
              </Text>
              <Button
                title="Reset Password"
                iconName="key"
                variant="secondary"
                onPress={handleResetPassword}
                disabled={
                  isResendingVerification || isResettingPassword || isDeleting
                } // Disable if any operation is in progress
                isLoading={isResettingPassword} // Pass specific loading state
                // className prop is optional now
              />
            </>
          )}

          {!isPasswordProvider && (
            <Text className="text-sm text-text-secondary dark:text-text-secondary-dark">
              You're signed in with a third-party provider. Password management
              is handled by your provider.
            </Text>
          )}
        </AnimatedCard>

        <AnimatedCard className="p-5 mt-4" dangerZone={true}>
          <View className="flex-row items-center justify-between mb-5">
            <Text className="text-xl font-semibold text-error dark:text-error-dark">
              Danger Zone
            </Text>
            <IconBadge
              name="alert-triangle"
              color="#EF4444"
              bgColor="bg-error/20 dark:bg-error-dark/30"
            />
          </View>

          <Text className="mb-5 text-sm text-text-secondary dark:text-text-secondary-dark">
            Deleting your account is permanent. All your data, including
            profiles and gift history, will be permanently removed.
          </Text>

          <Button
            title="Delete Account"
            iconName="trash-2"
            variant="error"
            disabled={
              isResendingVerification || isResettingPassword || isDeleting
            } // Disable if any operation is in progress
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
              setIsDeleteModalVisible(true);
            }}
            isLoading={isDeleting} // Pass specific loading state
            // className prop is optional now
          />
        </AnimatedCard>
      </ScrollView>

      <Animated.View
        style={animatedLoaderStyle}
        className="absolute inset-0 z-10 flex items-center justify-center bg-background/80 dark:bg-background-dark/80"
      >
        {isDeleting && ( // Use isDeleting here
          <View className="items-center p-5 shadow-lg bg-card dark:bg-card-dark rounded-2xl">
            <ActivityIndicator size="large" color="#E87900" />
            <Text className="mt-3 font-medium text-text-primary dark:text-text-primary-dark">
              Deleting Account...
            </Text>
          </View>
        )}
      </Animated.View>

      <Modal
        animationType="fade"
        transparent={true}
        visible={isDeleteModalVisible}
        onRequestClose={() => setIsDeleteModalVisible(false)}
      >
        <Pressable
          className="absolute inset-0 items-center justify-center flex-1 bg-black/60 backdrop-blur-sm"
          onPress={() => setIsDeleteModalVisible(false)} // Dismiss on background press
        >
          <Pressable
            className="w-11/12 max-w-md p-6 m-5 shadow-xl rounded-xl bg-card dark:bg-card-dark"
            onPress={() => {}} // Consume press event
          >
            <Animated.View
              entering={FadeIn.duration(200)}
              exiting={FadeOut.duration(200)}
            >
              <View className="items-center mb-4">
                <View className="items-center justify-center w-12 h-12 mb-3 rounded-full bg-error/20 dark:bg-error-dark/30">
                  <Feather name="trash-2" size={24} color="#EF4444" />
                </View>
                <Text className="text-xl font-bold text-text-primary dark:text-text-primary-dark">
                  Delete Account?
                </Text>
              </View>

              <Text className="mb-5 text-center text-text-secondary dark:text-text-secondary-dark">
                This action is permanent and cannot be undone. All your data
                will be deleted.
              </Text>

              {isPasswordProvider && (
                <View className="mb-4">
                  <Text className="mb-2 text-sm font-medium text-text-primary dark:text-text-primary-dark">
                    Please enter your password to confirm:
                  </Text>

                  <Input
                    placeholder="Password"
                    secureTextEntry
                    value={passwordForReauth}
                    onChangeText={setPasswordForReauth}
                    className="px-3 py-2 border rounded-md border-border dark:border-border-dark text-text-primary dark:text-text-primary-dark"
                    // Add any other necessary props for your Input component
                  />
                </View>
              )}

              <View className="flex-row justify-center space-x-3">
                <Button
                  title="Cancel"
                  variant="outline"
                  className="flex-1" // Add flex-1 for equal width
                  onPress={() => {
                    setIsDeleteModalVisible(false);
                    setPasswordForReauth(''); // Clear password on cancel
                  }}
                />
                <Button
                  title="Confirm Delete" // Changed button title
                  iconName="trash-2"
                  variant="error"
                  className="flex-1" // Add flex-1 for equal width
                  onPress={handleConfirmDelete} // Wired to the new handler
                  disabled={
                    isDeleting || (isPasswordProvider && !passwordForReauth)
                  } // Disable while deleting or if password needed but not entered
                  isLoading={isDeleting} // Pass isDeleting to Button component
                />
              </View>
            </Animated.View>
          </Pressable>
        </Pressable>
      </Modal>
    </SafeAreaView>
  );
};

export default PersonalInfoScreen;
