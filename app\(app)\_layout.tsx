import { Tabs } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import { useColorScheme } from 'nativewind';
import { Platform, Image } from 'react-native';

import usePushNotifications from '../../hooks/usePushNotifications';
import { colors } from '../../constants/Colors'; // Assumed to potentially have 'textSecondary' now
import HomeIcon from '../../assets/images/home.png';
import CalendarIcon from '../../assets/images/calendarIcon.png';
import SearchIcon from '../../assets/images/searchIcon.png';
import SettingsIcon from '../../assets/images/settingsIcon.png';

// Define a precise type for Feather icon names for reusability and clarity.
type FeatherIconName = React.ComponentProps<typeof Feather>['name'];

// Define the set of valid tab names. This can be leveraged by Expo Router's typed routes if configured.
// This ensures that only valid route names are used in the configuration.
type TabRouteName = 'home' | 'search' | 'calendar' | 'settings';

// Define the structure for individual tab screen configurations.
// This enhances type safety and code readability.
interface TabScreenConfig {
  readonly name: TabRouteName;
  readonly title: string;
  readonly iconName: FeatherIconName;
  readonly accessibilityLabel: string;
  readonly headerShown?: boolean; // Optional: if not set, global screenOptions.headerShown applies.
  readonly imageSource?: any;
}

const tabScreenConfigs: readonly TabScreenConfig[] = [
  {
    name: 'home',
    title: 'Home',
    iconName: 'gift',
    accessibilityLabel: 'Home Tab, Gift Recommendations',
    headerShown: false,
    imageSource: HomeIcon,
    // Example: if 'home' specifically needs a header, set: headerShown: true
  },
  {
    name: 'search',
    title: 'Search',
    iconName: 'search',
    accessibilityLabel: 'Search Tab, Find Gift Ideas',
    headerShown: false,
    imageSource: SearchIcon,
  },
  {
    name: 'calendar',
    title: 'Calendar',
    iconName: 'calendar',
    accessibilityLabel: 'Calendar Tab, Important Dates',
    headerShown: false,
    imageSource: CalendarIcon,
  },
  {
    name: 'settings',
    title: 'Settings',
    iconName: 'settings',
    accessibilityLabel: 'Settings Tab, App Configuration',
    headerShown: false, // Explicitly hide header for settings, overriding the global default if it were true.
    imageSource: SettingsIcon,
  },
];

interface TabBarIconProps {
  name: FeatherIconName;
  color: string;
  size: number;
  // 'focused' prop removed as its primary effect (color change) is handled by Expo Router
  // supplying the correct 'color' based on active/inactive state.
}

// A dedicated component for tab icons enhances clarity and reusability.
const TabBarIcon: React.FC<TabBarIconProps> = ({ name, color, size }) => {
  return <Feather name={name} size={size} color={color} />;
};

export default function AppLayout() {
  usePushNotifications(); // Initialize push notifications
  const { colorScheme } = useColorScheme(); // For dark mode awareness

  // Define theme-aware colors based on the Giftmi Style Guide
  const activeTintColor =
    colorScheme === 'dark' ? colors.primary.dark : colors.primary.DEFAULT;
  // Assuming 'text-secondary' in Colors.ts might be refactored to 'textSecondary'
  // If not, revert to: colors['text-secondary'].dark and colors['text-secondary'].DEFAULT
  const tabBarBackgroundColor =
    colorScheme === 'dark' ? colors.card.dark : colors.card.DEFAULT;
  const tabBarBorderColor =
    colorScheme === 'dark' ? colors.border.dark : colors.border.DEFAULT;

  return (
    <Tabs
      screenOptions={{
        headerShown: false, // Global Default: hide headers for all tab screens.
        // Individual screens can override this in their `options`.
        tabBarActiveTintColor: activeTintColor,
        tabBarStyle: {
          backgroundColor: tabBarBackgroundColor,
          borderTopWidth: 1,
          borderTopColor: tabBarBorderColor,
          ...(Platform.OS === 'ios'
            ? {
                // iOS shadow color now uses the theme-aware tabBarBorderColor
                shadowColor: tabBarBorderColor,
                shadowOffset: { width: 0, height: -0.5 },
                shadowOpacity: 0.1,
                shadowRadius: 2, // Adjusted for a subtle blur (LOW 1)
              }
            : {
                // Android: elevation: 0 is maintained for a flat design,
                // can be adjusted if a shadow is desired.
                elevation: 0,
              }),
        },
        tabBarShowLabel: true,
        tabBarLabelStyle: {
          // TODO: Define font family and size once typography scale is finalized in the style guide.
          // Tracked: Ensure this is addressed per project management.
        },
      }}
    >
      {tabScreenConfigs.map((tab) => (
        <Tabs.Screen
          key={tab.name}
          // HIGH 1: Removed `as any`. The `tab.name` is now of type `TabRouteName`,
          // which is a union of string literals. This should be compatible with
          // Expo Router's `name` prop, which expects a string corresponding to a route.
          // If Expo Router's typed routes are set up, `TabRouteName` should ideally align
          // with or be derived from the generated route types for maximum safety.
          name={tab.name}
          options={{
            title: tab.title,
            tabBarIcon: (
              { color, size, focused } // `focused` is available from Expo Router
            ) => {
              const iconSize = size + 14; // Increase size slightly
              if (tab.imageSource) {
                return <Image source={tab.imageSource} style={{ width: iconSize, height: iconSize, opacity: focused ? 1 : 0.5 }} />;
              }
              return (
                <TabBarIcon
                  name={tab.iconName}
                  color={color} // `color` is already focus-aware
                  size={iconSize}
                  // `focused` prop is no longer passed to TabBarIcon (MEDIUM 1)
                />
              );
            },
            tabBarAccessibilityLabel: tab.accessibilityLabel,
            // HIGH 2 & MEDIUM 3: Corrected `headerShown` logic.
            // If `tab.headerShown` is defined (e.g., `false` for settings), it will be used.
            // If `tab.headerShown` is `undefined` (e.g., for home, search, calendar by default),
            // Expo Router will correctly fall back to `screenOptions.headerShown` (which is `false`).
            // To show a header for a specific tab, set `headerShown: true` in its config.
            headerShown: tab.headerShown,
          }}
        />
      ))}

      {/* Screens that should NOT be tabs, hidden from the tab bar */}
      <Tabs.Screen name="profiles" options={{ href: null }} />
      {/* Add other non-tab screens here if necessary, e.g.: */}
      {/* <Tabs.Screen name="gifts/details" options={{ href: null }} /> */}
      {/* <Tabs.Screen name="gifts/add" options={{ href: null }} /> */}
    </Tabs>
  );
}
