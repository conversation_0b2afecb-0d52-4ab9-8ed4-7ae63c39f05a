import React, { useState } from 'react';
import { View, Text, Pressable } from 'react-native';
import { Link } from 'expo-router'; // Import Link for navigation
import Input from '@/components/ui/Input'; // Assuming alias setup
import <PERSON><PERSON> from '@/components/ui/Button'; // Assuming alias setup
import { useAuth } from '../../contexts/AuthContext'; // Import useAuth

export default function LoginScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null); // State for login errors
  const { signIn, signInWithGoogle, isLoading } = useAuth(); // Get signIn, signInWithGoogle, and isLoading from context

  const handleLogin = async () => {
    setError(null); // Clear previous errors

    // --- Start Input Validation ---
    if (!email || !password) {
      setError('Email and password are required.');
      return;
    }

    const emailRegex = /^[^@]+@[^@]+\.[^@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address.');
      return;
    }
    // --- End Input Validation ---

    console.log('Login attempt with:', { email }); // Keep email log, remove password log for security
    try {
      await signIn(email, password);
      // Navigation will be handled by the AuthProvider based on auth state change
    } catch (err: any) {
      console.error('Login failed:', err);
      // Basic error message, could be enhanced based on specific error codes
      setError(err.message || 'Login failed. Please check your credentials.');
    }
  };

  const handleGoogleSignIn = async () => {
    setError(null); // Clear previous errors
    console.log('Google Sign-in attempt...');
    try {
      await signInWithGoogle();
      // Navigation will be handled by the AuthProvider based on auth state change
    } catch (err: any) {
      console.error('Google Sign-in failed:', err);
      // Display a generic error for now, specific handling might be needed
      setError(err.message || 'Google Sign-in failed. Please try again.');
    }
  };

  return (
    <View className="items-center justify-center flex-1 p-6 bg-background">
      <View className="w-full max-w-sm">
        {/* Use text-text-primary, adjusted size and margin */}
        <Text className="mb-6 text-2xl font-bold text-center text-text-primary">
          Login
        </Text>

        {/* Add gap for spacing between inputs */}
        <View className="gap-4 mb-4">
          <Input
            placeholder="Email"
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
            // Apply theme styles: border, background, padding, text color
            className="p-3 border rounded-md border-border bg-input-background text-text-primary"
            editable={!isLoading} // Disable input while loading
          />
          <Input
            placeholder="Password"
            value={password}
            onChangeText={setPassword}
            secureTextEntry
            // Apply theme styles: border, background, padding, text color
            className="p-3 border rounded-md border-border bg-input-background text-text-primary"
            editable={!isLoading} // Disable input while loading
          />
        </View>

        {/* Display login error message */}
        {error && (
          // Removed comment causing syntax error
          <Text className="mb-4 text-sm text-center text-feedback-error">{error}</Text>
        )}

        <Button
          title="Login"
          onPress={handleLogin}
          className="mb-6" // Adjusted margin
          isLoading={isLoading} // Pass isLoading to Button
          disabled={isLoading} // Also disable button explicitly
          variant="secondary" // Use secondary variant for distinction
        />

        {/* Separator */}
        <View className="flex-row items-center my-4">
          <View className="flex-1 h-px bg-border" />
          <Text className="mx-4 text-xs text-muted-foreground">OR</Text>
          <View className="flex-1 h-px bg-border" />
        </View>

        {/* Google Sign-in Button */}
        <Button
          title="Sign in with Google"
          onPress={handleGoogleSignIn}
          variant="secondary" // Use secondary variant for distinction
          className="mb-6" // Consistent margin
          isLoading={isLoading} // Use the same loading state for now
          disabled={isLoading}
          // Optional: Add an icon here if desired using libraries like @expo/vector-icons
          // icon={<AntDesign name="google" size={18} color="your_color" />} // Example
        />


        <Link href="/signup" asChild>
          <Pressable disabled={isLoading}>
            {/* Use primary-500 for the link */}
            <Text className="text-center underline text-primary-500">
              Don't have an account? Sign Up
            </Text>
          </Pressable>
        </Link>
      </View>
    </View>
  );
}