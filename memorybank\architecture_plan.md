# Final Plan: AI-Integrated Gift Recommendation App ("Giftmi")

## 1. Introduction & Vision

*   **Purpose:** To help users find thoughtful and personalized gifts for their Loved Ones by leveraging user-provided information about their partner and recommendations generated by a Large Language Model (LLM).
*   **Goal:** Reduce the stress of gift-giving, improve gift quality, and strengthen relationships through meaningful presents.
*   **Target User:** Individuals in relationships looking for gift ideas for their partners.

## 2. Core Features

*   **User Authentication:** Secure login/signup (Firebase Auth).
*   **Loved One Profile Management:** Create, view, edit, and delete profiles for Loved Ones.
*   **Personalized Data Input:** Sections within the SO profile for: Basic Info, Important Dates, Interests & Hobbies, Preferences, Sizes, Dislikes, Wishlist Items, Past Gifts, General Notes.
*   **AI Recommendation Engine (LLM-Based):** Generates personalized gift suggestions by sending curated SO profile data to an external LLM API (e.g., GPT, Gemini) via Firebase Cloud Functions.
*   **Gift Idea Management:** Browse, search, filter, save, mark as purchased, link gift ideas (now integrated into the home screen).
*   **Date Tracking & Reminders:** Calendar/list of important dates with push notification reminders.
*   **User Settings:** Budget preferences, notification settings.

## 3. Loved One Profile & Data Management

*   **Purpose:** Central repository *within the app* for user-provided information about their Loved One, feeding the LLM prompt.
*   **Data Schema (Firestore Collection: `significant_others`):**
    *   `userId`: (Reference to `users` collection)
    *   `profileId`: (Unique ID)
    *   `name`: String
    *   `relationship`: String
    *   `birthday`: Timestamp (optional)
    *   `anniversary`: Timestamp (optional)
    *   `interests`: Array of Strings
    *   `dislikes`: Array of Strings
    *   `preferences`: Object (e.g., `favoriteColor`, `preferredStyle`, `favoriteBrands`)
    *   `sizes`: Object (e.g., `clothing`, `shoe`)
    *   `wishlistItems`: Array of Objects (`item`: String, `notes`: String, `link`: String (optional), `dateAdded`: Timestamp) - *Consider pagination for display if list grows large.*
    *   `pastGiftsGiven`: Array of Objects (`item`: String, `occasion`: String, `date`: Timestamp, `reaction`: String (optional)) - *Consider pagination.*
    *   `generalNotes`: Array of Objects (`note`: String, `date`: Timestamp) - *Consider pagination and potential future need for search/filtering within notes.*
    *   `interests`: Array of Strings - *MVP: Simple strings. Future: Consider structured tags/ontology for better LLM input.*
    *   `dislikes`: Array of Strings - *MVP: Simple strings. Future: Consider structured tags.*
*   **Data Input:** Design intuitive forms. Employ progressive disclosure to avoid overwhelming users initially. Consider gamification or prompts to encourage detailed input over time. Address the "Cold Start" problem for new profiles with guided setup or initial generic suggestions. Plan for managing stale data (e.g., periodic prompts to review/update profile sections).
*   **Data Privacy & Security:** Strict Firestore Security Rules are paramount.
    *   *Principle:* Users can only read/write their own `user` profile and associated `significant_others` documents.
    *   *Validation:* Implement server-side validation rules in Firestore to ensure data integrity (e.g., data types, required fields).
    *   *Function Access:* Cloud Functions should validate user authentication context before accessing data. Consider specific rules granting access only to necessary functions.

## 4. AI Integration Details (LLM-Based)

*   **Data Sources:** Primarily the `Loved One Profile` data in Firestore. User interactions (saved, dismissed, marked purchased, rated/flagged gifts) **will** be used as a feedback loop.
*   **Recommendation Approach:**
    *   **LLM Integration:** Utilize Firebase Cloud Functions to:
        1.  Fetch the relevant SO profile data from Firestore.
        2.  Construct a detailed, structured prompt containing key information (interests, preferences, dislikes, sizes, occasion, budget constraints, past gift history, wishlist items, etc.).
        3.  Send the prompt securely to the chosen LLM API (e.g., OpenAI API, Google AI API).
        4.  Receive the generated gift recommendations.
        5.  Parse and format the recommendations for display in the app.
    *   **Prompt Engineering:** Crucial and requires ongoing effort.
        *   *Strategy:* Develop versioned prompts. Implement A/B testing or evaluation framework (define metrics for "good" recommendations - e.g., relevance score, click-through/save rate).
        *   *Content:* Include negative constraints (dislikes), positive signals (interests, preferences), occasion context, budget, past gift feedback, desired output format (JSON).
        *   *Optimization:* Tailor prompt length/detail based on context to manage token usage/cost.
    *   **Recommendation Quality & Handling:**
        *   *Fallback:* Implement fallback logic if LLM API fails or returns unusable results (e.g., generic suggestions based on interests, error message).
        *   *User Feedback:* Allow users to rate/flag poor recommendations. This data feeds into the feedback loop.
    *   **Feedback Loop:** User interactions (save, dismiss, purchase, ratings) must feed back into the system.
        *   *MVP Approach:* Use feedback to filter future results from the LLM or slightly adjust prompt weighting.
        *   *Future:* Consider using feedback data for fine-tuning a model (if feasible/cost-effective) or more sophisticated prompt adjustments.
    *   **Recommendation Freshness:** Recommendations are generated on-demand when requested by the user. Consider short-term caching (e.g., few minutes) of results for identical profile states/requests to reduce LLM calls, but prioritize freshness based on profile updates.
    *   **Filtering/Refinement:** Apply client-side or server-side filtering based on user settings (e.g., budget) if not handled sufficiently by the LLM prompt.
*   **AI Model/Service:**
    *   **Choice:** OpenAI (GPT-3.5/4) or Google (Gemini) are strong candidates. Decision depends on API features, pricing, and performance.
    *   **API Key Management:** Store LLM API keys securely using Firebase Cloud Functions environment variables or a dedicated secret manager (like Google Secret Manager). **Never commit keys to the repository.**
    *   **Cost & Latency Management:**
        *   *LLM Costs:* Monitor API usage closely. Implement budget alerts (Google Cloud Billing). Define strategy for handling high usage (e.g., rate limiting per user, notify user). Implement robust retry logic with backoff for transient LLM API errors. Explore batching requests if applicable, though personalization may limit this.
        *   *Cloud Function Performance:* Optimize function memory/CPU allocation. Choose appropriate regions to minimize latency. Monitor cold starts; consider provisioned concurrency if latency becomes critical (trade-off with cost).
        *   *Firestore Costs:* Design queries efficiently. Implement pagination for large data arrays/collections accessed by the client. Monitor read/write operations. Be mindful of document size limits.
    *   **Data Privacy:** Minimize PII sent in prompts. Anonymize or abstract data where possible. Clearly document data sent to LLM in privacy policy. Adhere to LLM provider's data usage terms.

## 5. Architecture Overview

```mermaid
graph TD
    A[User (React Native App)] --> B{Firebase Authentication};
    A -- Reads/Writes SO Profile --> C[Firebase Firestore];
    A --> D[Firebase Storage (Optional)];
    C -- SO Profile Data --> E{Firebase Cloud Functions};
    E -- Secure API Call w/ Prompt --> F[External LLM API (GPT/Gemini)];
    F -- Recommendations --> E;
    E -- Formatted Recs --> A; // Or save to Firestore first
    E -- Reads API Key --> I[Secret Management (Firebase Env Vars / Google Secret Manager)];
    C -- Date Info --> G[Firebase Cloud Functions (Notifications)];
    G -- Triggers --> H[Firebase Cloud Messaging];
    H --> A;

    subgraph Firebase Backend
        B
        C
        D
        E
        G
        H
        I
    end

    subgraph External Services
        F
    end
```

*   **Frontend:** React Native with Expo, NativeWind, React Navigation.
*   **State Management:** React Context API (Auth), Zustand/Jotai for local/feature state.
*   **Backend:** Firebase (Authentication, Firestore, Cloud Functions, Storage).
*   **AI:** Firebase Cloud Functions orchestrating external LLM API calls.
*   **Monitoring:** Google Cloud Monitoring & Logging (for Cloud Functions, API errors, performance metrics), Firebase Console (Auth, Firestore usage). Define key metrics to track (e.g., LLM API latency/error rates, function execution time, Firestore reads/writes).
*   **CI/CD:** *To be defined* (e.g., GitHub Actions + EAS Build/Submit).

## 6. Tech Stack Rules & Best Practices

*   Use functional components and Hooks in React Native.
*   Structure components logically (`components/ui`, `components/featureX`, `screens`).
*   Utilize `expo-router` for file-based routing.
*   Style consistently using NativeWind utility classes. Define custom themes/colors in `tailwind.config.js`.
*   Optimize performance: `React.memo`, `useCallback`, FlatList optimizations.
*   **Firestore:** Design scalable data models. Use specific queries; implement pagination client-side/server-side where needed. Implement robust Security Rules (user data isolation, function-specific access, input validation). Utilize indexes (`firestore.indexes.json`). Monitor usage and costs.
*   **Cloud Functions:** Write efficient, idempotent functions. Optimize region, memory. Use environment variables/Secret Manager for secrets. Implement robust error handling and logging (structured logs preferred). Secure endpoints (validate Auth context, consider App Check). Monitor performance and errors.
*   **Authentication:** Implement secure Firebase Authentication flows. Handle token refresh.
*   **TypeScript:** Enable `strict` mode. Define clear interfaces/types (`types/index.ts` or feature-specific).
*   **Code Quality:** Use Prettier and ESLint for consistent code style and error prevention.
*   **Git:** Use meaningful commit messages (e.g., Conventional Commits). Employ a branching strategy (e.g., Gitflow). Keep `.gitignore` updated.
*   **Secrets Management:** **Never commit secrets.** Use Firebase env vars or Google Secret Manager.
*   **Testing:** Implement comprehensive testing:
    *   Unit Tests (Jest): For utility functions, complex logic.
    *   Integration Tests (Jest/RNTL): For component interactions, Cloud Function logic interacting with Firebase/LLM APIs (using emulators/mocks where appropriate).
    *   E2E Tests (Detox/Maestro): *Consider for critical user flows later.*
*   **Error Handling:** Implement robust error handling at all layers (UI, state management, API calls, Cloud Functions). Provide informative feedback to the user.
*   **CI/CD:** Establish a CI/CD pipeline early for automated testing and deployments.
*   **Documentation:** Maintain clear, concise, and up-to-date project documentation (`memorybank/`).

## 7. Project Documentation ("Memory Bank")

*   **Purpose:** This directory (`memorybank/`) serves as the central repository for all project-related documentation.
*   **Contents (Examples):** `project_brief.md`, `tech_stack.md`, `architecture.md` (this file), `style_guide.md`, `coding_standards.md`, `setup_guide.md`, `todo.md`.
*   **Maintenance:** Keep documentation version-controlled and updated regularly.

## 8. Initial To-Do List (LLM Focus)

1.  **Project Setup:** (Firebase, Expo, NativeWind, Git Repo, CI/CD Basics)
2.  **Core UI Shell:** (Navigation, basic screens, reusable components)
3.  **Authentication Flow:** (Login, Signup, Logout, Auth state management)
4.  **Loved One Profile Management:** (Firestore schema, Forms, CRUD, Security Rules, Initial Pagination for lists)
5.  **Basic Recommendation Display:** (Gift Ideas screen UI structure)
6.  **AI Integration - Phase 1 (Setup & Security):**
    *   [ ] Set up Cloud Functions environment (TypeScript).
    *   [ ] Choose LLM provider & get API key.
    *   [ ] Configure secure API key storage (Env Vars/Secret Manager).
    *   [ ] Create `getGiftRecommendations` Cloud Function (HTTP, Auth validated).
    *   [ ] Install LLM SDK.
    *   [ ] Define initial monitoring/logging strategy for functions.
7.  **AI Integration - Phase 2 (LLM Call & Prompting V1):**
    *   [ ] Implement SO profile data fetching in function.
    *   [ ] Develop V1 prompt structure & content generation logic.
    *   [ ] Implement LLM API call with basic error handling & retry logic.
    *   [ ] Parse LLM response.
    *   [ ] Connect "Gift Ideas" screen to function & display results.
    *   [ ] Define initial prompt evaluation metrics.
8.  **AI Integration - Phase 3 (Feedback & Quality):**
    *   [ ] Implement user rating/flagging UI for recommendations.
    *   [ ] Store feedback data in Firestore.
    *   [ ] Implement V1 feedback loop logic (e.g., filtering results based on dismiss/negative flags).
    *   [ ] Implement fallback strategy for LLM failures/bad results.
9.  **Date Tracking & Notifications:** (Calendar/List, Scheduled Function, FCM setup & basic trigger)
10. **Operational Readiness:**
    *   [ ] Set up basic CI/CD pipeline (Lint, Test, Build).
    *   [ ] Configure detailed monitoring & alerting (Function performance, LLM errors, Costs).
    *   [ ] Implement comprehensive integration tests for core flows (Auth, Profile CRUD, AI Recs).
11. **UX Refinements:**
    *   [ ] Implement "Cold Start" UX for new profiles.
    *   [ ] Refine progressive data input flow for SO profile.
    *   [ ] Define recommendation presentation format (images, links?).
12. **Project Documentation ("Memory Bank"):**
    *   [ ] Update `architecture_plan.md` (this file).
    *   [ ] Populate `style_guide.md` and `coding_standards.md`.
    *   [ ] Keep all docs updated.