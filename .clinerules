# Giftmi Coding Standards

This document defines the coding conventions, linting rules, naming conventions, and best practices for the Giftmi codebase. Adhering to these standards ensures code consistency, readability, and maintainability.

## General Principles

*   **Readability:** Write code that is easy for other developers (and your future self) to understand.
*   **Consistency:** Apply these standards uniformly across the codebase.
*   **Simplicity (KISS):** Prefer simple solutions over complex ones unless necessary.
*   **Dry (Don't Repeat Yourself):** Avoid duplicating code; use functions, components, and constants.

## Language Specific Standards

### TypeScript (v5.3.3)

*   **Strict Mode:** Enable `strict` mode in `tsconfig.json` (already recommended in architecture).
*   **Typing:**
    *   Provide explicit types for function parameters, return values, and variables where type inference is not obvious or sufficient.
    *   Use interfaces (`interface`) for defining the shape of objects and classes. Use types (`type`) for unions, intersections, primitives, and function signatures.
    *   Avoid `any` where possible. Use `unknown` for values with truly unknown types and perform type checking.
    *   Use utility types (e.g., `Partial`, `Readonly`, `Pick`, `Omit`) effectively.
    *   Define shared types in `types/index.ts` or feature-specific type files (e.g., `types/api.ts`, `types/firestore.ts`).
*   **Modules:** Use ES Modules (`import`/`export`). Prefer named exports over default exports for better discoverability and refactoring.

### React (v18.3.1) / React Native (v0.76.9)

*   **Functional Components:** Use functional components with Hooks exclusively. Avoid class components.
*   **Hooks:**
    *   Follow the Rules of Hooks (call Hooks only at the top level, call Hooks only from React functions).
    *   Use `useState` for simple component state.
    *   Use `useEffect` for side effects (data fetching, subscriptions). Include specific dependencies in the dependency array. Clean up effects (e.g., unsubscribe) in the return function.
    *   Use `useContext` for accessing global state (e.g., Auth).
    *   Use `useCallback` and `React.memo` for performance optimizations where necessary and measured, especially with lists or frequently re-rendering components. Avoid premature optimization.
*   **Component Structure:**
    *   Keep components small and focused on a single responsibility.
    *   Organize components logically (e.g., `components/ui/`, `components/auth/`, `components/profile/`).
    *   Use descriptive names for props. Use object destructuring for props.
*   **JSX:**
    *   Use explicit `return` statements for multi-line JSX.
    *   Use fragments (`<>...</>`) when no wrapper DOM element is needed.
    *   Use conditional rendering clearly (ternaries for simple cases, `&&` for optional rendering, or extracted logic for complex cases).
*   **Accessibility (a11y):** Use appropriate accessibility props (`accessibilityLabel`, `accessibilityHint`, `accessibilityRole`, etc.) on components.

### NativeWind (v4.1.23) / Tailwind CSS (v3.4.17)

*   **Utility-First:** Embrace the utility-first approach. Apply styles directly in the `className` prop.
*   **Configuration:** Define custom colors, fonts, and spacing in `tailwind.config.js` to maintain consistency. Avoid arbitrary values in class names.
*   **Readability:** For complex or frequently reused style combinations, consider creating custom components that encapsulate the styles or use `@apply` sparingly within a global CSS file (`global.css`) for specific base styles or complex patterns.
*   **Platform Specifics:** Use platform prefixes (e.g., `ios:`, `android:`) when necessary.

### Node.js (for Firebase Cloud Functions)

*   **TypeScript:** Write Cloud Functions in TypeScript for type safety.
*   **Async/Await:** Use `async/await` for handling asynchronous operations (Firestore calls, LLM API calls).
*   **Error Handling:** Implement robust error handling using `try...catch` blocks. Log errors effectively using `functions.logger`. Return meaningful error responses from HTTP functions.
*   **Idempotency:** Design functions to be idempotent where possible (retrying the function produces the same result).
*   **Security:** Always validate input data. Validate user authentication context (`context.auth`). Avoid exposing sensitive information in logs or error messages.
*   **Efficiency:** Optimize Firestore queries. Minimize external API calls. Choose appropriate function regions and memory settings.

## Code Formatting (Prettier)

*   Configure Prettier (via `.prettierrc.js` or `package.json`) with agreed-upon settings (e.g., print width, tab width, semi-colons, quotes).
*   Integrate Prettier with the editor and potentially as a pre-commit hook to ensure consistent formatting automatically.

## Linting Rules (ESLint with Expo Lint)

*   Use the configuration provided by `expo lint` (`eslint-config-expo`) as a base.
*   Customize rules in `.eslintrc.js` as needed to enforce specific project standards (e.g., React Hooks rules, TypeScript rules).
*   Address all linting errors and warnings. Integrate ESLint into the editor and CI pipeline.

## Naming Conventions

*   **Variables/Functions:** `camelCase` (e.g., `userName`, `fetchUserProfile`).
*   **Constants:** `UPPER_SNAKE_CASE` (e.g., `API_TIMEOUT`, `DEFAULT_AVATAR`).
*   **Components:** `PascalCase` (e.g., `UserProfileCard`, `PrimaryButton`). File names should match component names (e.g., `UserProfileCard.tsx`).
*   **Interfaces/Types:** `PascalCase` (e.g., `UserProfile`, `AuthContextType`).
*   **Files:** Prefer `kebab-case` for utility files or non-component files (e.g., `api-client.ts`, `date-utils.ts`). Use `PascalCase` for component files.
*   **Boolean Variables:** Prefix with `is`, `has`, `should` (e.g., `isLoading`, `hasCompletedProfile`, `shouldShowModal`).

## Commenting Guidelines

*   Write comments to explain *why* code exists, not *what* it does (the code should explain the *what*).
*   Explain complex logic, workarounds, or business rules.
*   Use TSDoc (`/** ... */`) for documenting functions, types, and interfaces, especially for shared utilities or complex components.
*   Remove commented-out code before committing. Use version control history instead.
*   Use `// TODO:` or `// FIXME:` comments for temporary notes, but track these tasks properly in the issue tracker (`memorybank/todo.md` or external tool).

## Error Handling Practices

*   Handle errors gracefully at all layers (UI, API calls, Functions).
*   Use `try...catch` blocks for operations that might fail (API calls, async operations).
*   Provide informative error messages to the user where appropriate, without exposing sensitive details.
*   Log errors server-side (Cloud Functions) with sufficient context for debugging.
*   Define specific error types or codes if needed for complex error handling scenarios.

## Testing Standards

*   **Unit Tests (Jest):** Test individual functions, utilities, and simple components in isolation. Aim for good coverage of logic branches. Use mocking effectively.
*   **Integration Tests (Jest/RNTL):** Test interactions between components, state management, and mocked API/function calls. Verify component rendering based on props/state. Test Cloud Function logic with Firebase emulators or mocks.
*   **Test Naming:** Describe what the test is verifying clearly (e.g., `it('should return user profile on successful fetch')`).
*   **Test Structure:** Use Arrange-Act-Assert pattern where applicable.

## Git Workflow & Commit Message Format

*   **Branching:** Use a feature branching workflow (e.g., Gitflow variation). Create branches from `main` or `develop` for features/fixes.
*   **Commits:** Make small, atomic commits.
*   **Commit Messages:** Follow Conventional Commits format (e.g., `feat: add user login screen`, `fix: correct calculation error`, `refactor: improve data fetching logic`, `docs: update coding standards`). This helps with automated changelog generation and understanding history.
*   **Pull Requests (PRs):** Use PRs for merging feature branches. Require code reviews. Ensure CI checks (linting, testing) pass before merging.