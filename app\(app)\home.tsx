// ~/app/(app)/home.tsx
import { AddGeneralNoteModal } from '../../components/profile/AddGeneralNoteModal';
import { v4 as uuidv4 } from 'uuid'; // LOW 6: Use uuid for temporary IDs
import { Timestamp } from 'firebase/firestore';
import { updateSignificantOther } from '../../services/profileService';
import React, {
  useEffect,
  useState,
  useRef,
  useCallback,
  useMemo,
} from 'react';
import {
  View,
  Text,
  Pressable,
  Modal,
  FlatList,
  SafeAreaView,
  ScrollView,
  // Image, // Not used after removing commented-out logo
  TouchableOpacity,
  Alert, // For user feedback
} from 'react-native';
import {
  useFocusEffect,
  // useNavigation, // Not directly used in this snippet, router is used
  useRouter,
} from 'expo-router';
import { Feather, MaterialCommunityIcons } from '@expo/vector-icons';
import Animated, {
  FadeIn,
  // FadeOut, // Not explicitly used in this snippet
  SlideInRight,
  useAnimatedStyle,
  useSharedValue,
  withSequence,
  withTiming,
} from 'react-native-reanimated';
import { Link } from 'expo-router';
import * as Haptics from 'expo-haptics';
import Button from '../../components/ui/Button';
import LoadingIndicator from '../../components/ui/LoadingIndicator';
import Card from '../../components/ui/Card';
import { useAuth } from '../../contexts/AuthContext';
import { getSignificantOthers } from '../../services/profileService';
import {
  fetchGiftRecommendations,
  saveRecommendationFeedback,
  getProfileFeedback,
  deleteRecommendationFeedback,
  type FeedbackEntry, // Assuming FeedbackEntry type is defined
  type GiftRecommendation, // Assuming GiftRecommendation type is defined
} from '../../services/recommendationService';
import { SignificantOtherProfile } from '../../functions/src/types/firestore'; // Adjust path if needed
import AsyncStorage from '@react-native-async-storage/async-storage';
import ActionMenu from '../../components/home/<USER>';
import MotivationalHeader from '../../components/home/<USER>';
import NavigationGrid from '../../components/home/<USER>';
import UpcomingDatesDisplay from '../../components/home/<USER>';
import AddCustomDateModal from '../../components/profile/AddCustomDateModal';
import AddPastGiftModal from '../../components/profile/AddPastGiftModal';
import ProfileCompletionBanner from '../../components/home/<USER>';
import KeyDatesDisplay from '../../components/home/<USER>';
import useCalendarData from '../../hooks/useCalendarData';
import { useColorScheme } from 'nativewind'; // Or useThemeManager

const AnimatedCard = Animated.createAnimatedComponent(Card);
const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

const SELECTED_PROFILE_KEY = 'selectedProfileId';
const PROFILES_CACHE_KEY = 'cachedProfiles';
const CACHE_EXPIRY_KEY = 'cacheExpiry';
const CACHE_EXPIRY_TIME = 1000 * 60 * 30; // 30 minutes
const PROFILES_LAST_UPDATED_KEY = 'profilesLastUpdated';

export default function HomeScreen() {
  // const navigation = useNavigation(); // Not used
  const router = useRouter();
  const { user } = useAuth();
  const { colorScheme } = useColorScheme(); // Using NativeWind's hook
  const isDark = colorScheme === 'dark';

  // Calendar data for upcoming dates display
  const { upcomingDates } = useCalendarData();

  const [profiles, setProfiles] = useState<SignificantOtherProfile[] | null>(
    null
  );
  const [selectedProfileId, setSelectedProfileId] = useState<string | null>(
    null
  );
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [isMenuVisible, setIsMenuVisible] = useState(false);
  const [isNoteModalVisible, setIsNoteModalVisible] = useState(false);
  const [isDateModalVisible, setIsDateModalVisible] = useState(false);
  const [isGiftModalVisible, setIsGiftModalVisible] = useState(false);

  const [loading, setLoading] = useState<boolean>(true); // Main data loading
  const [error, setError] = useState<string | null>(null); // Main data error

  const [recommendations, setRecommendations] = useState<
    GiftRecommendation[] | null
  >(null);
  const [recommendationsLoading, setRecommendationsLoading] =
    useState<boolean>(false);
  const [recommendationsError, setRecommendationsError] = useState<
    string | null
  >(null);

  const [currentFeedbackMap, setCurrentFeedbackMap] = useState<
    Map<string, FeedbackEntry>
  >(new Map());
  const [feedbackError, setFeedbackError] = useState<string | null>(null); // MEDIUM 1: Error state for feedback
  const processingFeedbackRef = useRef(new Set<string>()); // For feedback race condition (fix is present)

  const [dataInitialized, setDataInitialized] = useState(false);
  const [lastProfilesFetchTimestamp, setLastProfilesFetchTimestamp] =
    useState<number>(0);

  const dropdownScale = useSharedValue(1); // Not used in current JSX
  const headerOpacity = useSharedValue(0);
  const plusIconRotation = useSharedValue(0);
  const backdropOpacity = useSharedValue(0);
  // const flatListRef = useRef(null); // Not used

  const updateCache = async (key: string, data: any) => {
    try {
      await AsyncStorage.setItem(key, JSON.stringify(data));
      await AsyncStorage.setItem(CACHE_EXPIRY_KEY, Date.now().toString());
    } catch (e) {
      /* LOW 2: Removed console.error, handle silently or with a logger */
    }
  };

  const isCacheValid = async () => {
    try {
      const expiryTime = await AsyncStorage.getItem(CACHE_EXPIRY_KEY);
      if (!expiryTime) return false;
      return Date.now() - parseInt(expiryTime) < CACHE_EXPIRY_TIME;
    } catch {
      return false;
    }
  };

  const optimizedDataLoad = useCallback(
    async (forceRefresh = false, updateTimestamp = true) => {
      if (!user?.uid) {
        setLoading(false); // Ensure loading is false if no user
        setProfiles([]); // Clear profiles if no user
        setSelectedProfileId(null);
        setRecommendations(null);
        return;
      }

      try {
        setLoading(true);
        setError(null); // Clear previous main error

        const cacheValid = !forceRefresh && (await isCacheValid());
        const savedProfileIdPromise =
          AsyncStorage.getItem(SELECTED_PROFILE_KEY);
        let significantOthers: SignificantOtherProfile[] = [];

        if (cacheValid) {
          try {
            const cachedProfilesString = await AsyncStorage.getItem(
              PROFILES_CACHE_KEY
            );
            if (cachedProfilesString) {
              significantOthers = JSON.parse(cachedProfilesString);
              setProfiles(significantOthers);
            }
          } catch {
            /* LOW 2: Removed console.error */
          }
        }

        if (significantOthers.length === 0 || forceRefresh) {
          // Also fetch if forceRefresh
          significantOthers = await getSignificantOthers(user.uid);
          setProfiles(significantOthers || []);
          if (significantOthers && significantOthers.length > 0) {
            updateCache(PROFILES_CACHE_KEY, significantOthers);
          }
        }

        if (updateTimestamp) setLastProfilesFetchTimestamp(Date.now());

        const savedProfileId = await savedProfileIdPromise;
        let profileToSelectId = savedProfileId;

        if (savedProfileId) {
          const savedProfileExists = significantOthers.some(
            (p) => p.profileId === savedProfileId
          );
          if (!savedProfileExists) profileToSelectId = null; // Saved profile no longer exists
        }

        if (!profileToSelectId && significantOthers.length > 0) {
          profileToSelectId = significantOthers[0].profileId;
        }

        if (profileToSelectId) {
          if (profileToSelectId !== selectedProfileId || !recommendations) {
            // Fetch if ID changed or no recommendations
            setSelectedProfileId(profileToSelectId);
            await AsyncStorage.setItem(SELECTED_PROFILE_KEY, profileToSelectId); // Persist selection
            fetchRecommendations(profileToSelectId);
            fetchProfileFeedback(profileToSelectId);
          }
        } else {
          // No profiles exist or none selectable
          setSelectedProfileId(null);
          setRecommendations(null);
          setCurrentFeedbackMap(new Map());
        }

        headerOpacity.value = withSequence(
          withTiming(0, { duration: 0 }),
          withTiming(1, { duration: 800 })
        );
        setDataInitialized(true);
      } catch (err) {
        setError(
          err instanceof Error
            ? err.message
            : 'Failed to load data. Please try again.'
        );
      } finally {
        setLoading(false);
      }
    },
    [user?.uid, selectedProfileId, recommendations]
  ); // Added recommendations to ensure it runs if they are cleared

  useEffect(() => {
    if (user?.uid && !dataInitialized) {
      // Ensure user.uid exists before initial load
      optimizedDataLoad();
    } else if (!user?.uid) {
      // Handle case where user logs out or becomes null
      setLoading(false);
      setProfiles([]);
      setSelectedProfileId(null);
      setRecommendations(null);
      setError(null);
      setDataInitialized(false); // Reset dataInitialized if user logs out
    }
  }, [user?.uid, dataInitialized, optimizedDataLoad]);

  useEffect(() => {
    plusIconRotation.value = withTiming(isMenuVisible ? 45 : 0, {
      duration: 200,
    });
    backdropOpacity.value = withTiming(isMenuVisible ? 0.5 : 0, {
      duration: 200,
    });
  }, [isMenuVisible, plusIconRotation, backdropOpacity]);

  useFocusEffect(
    useCallback(() => {
      const checkForUpdates = async () => {
        if (!user?.uid) return; // Don't run if no user

        try {
          const lastUpdatedString = await AsyncStorage.getItem(
            PROFILES_LAST_UPDATED_KEY
          );
          const savedProfileId = await AsyncStorage.getItem(
            SELECTED_PROFILE_KEY
          );
          const lastUpdatedTimestamp = lastUpdatedString
            ? parseInt(lastUpdatedString, 10)
            : 0;

          let needsListRefresh =
            lastUpdatedTimestamp > lastProfilesFetchTimestamp;
          let needsSelectionUpdate = false;

          if (savedProfileId && savedProfileId !== selectedProfileId) {
            needsSelectionUpdate = true;
            // If selection changed, usually implies list might be same or updated,
            // but we prioritize selection change handling.
          }

          if (needsListRefresh || !dataInitialized) {
            optimizedDataLoad(true, true); // Force refresh profile list
          } else if (needsSelectionUpdate) {
            const currentProfiles = profiles || []; // Use current profiles if available
            const profileExists = currentProfiles.some(
              (p) => p.profileId === savedProfileId
            );
            if (profileExists) {
              setSelectedProfileId(savedProfileId);
              fetchRecommendations(savedProfileId!);
              fetchProfileFeedback(savedProfileId!);
            } else {
              // Saved profile ID no longer in list, force refresh list
              optimizedDataLoad(true, true);
            }
          } else if (
            selectedProfileId &&
            !recommendations &&
            !recommendationsLoading
          ) {
            // List is up to date, selection is current, but recommendations are missing
            fetchRecommendations(selectedProfileId);
            fetchProfileFeedback(selectedProfileId);
          }
        } catch {
          /* LOW 2: Error checking updates, silent fail or log to service */
        }
      };
      checkForUpdates();
    }, [
      user?.uid,
      dataInitialized,
      lastProfilesFetchTimestamp,
      selectedProfileId,
      profiles,
      recommendations,
      recommendationsLoading,
      optimizedDataLoad,
    ])
  );

  const fetchRecommendations = useCallback(async (profileId: string) => {
    if (!profileId) return;
    setRecommendationsLoading(true);
    setRecommendationsError(null);
    setRecommendations(null); // Clear old recommendations
    try {
      const result = await fetchGiftRecommendations(profileId);
      setRecommendations(result || []);
    } catch (err: any) {
      setRecommendationsError(
        'Failed to load recommendations. Please try again.'
      );
      setRecommendations(null);
    } finally {
      setRecommendationsLoading(false);
    }
  }, []); // Removed dependencies that were causing re-creation

  // MEDIUM 1: Modified fetchProfileFeedback
  const fetchProfileFeedback = useCallback(async (profileId: string) => {
    if (!profileId) return;

    setFeedbackError(null); // Reset feedback error
    try {
      const feedback = await getProfileFeedback(profileId);
      const feedbackMap = new Map<string, FeedbackEntry>();
      feedback.forEach((entry) => {
        if (entry.recommendationId) {
          // Ensure recommendationId exists
          feedbackMap.set(entry.recommendationId, entry);
        }
      });
      setCurrentFeedbackMap(feedbackMap);
    } catch (error) {
      setFeedbackError('Could not load feedback data.'); // Set user-facing error
      setCurrentFeedbackMap(new Map()); // Clear feedback map on error
    }
  }, []); // Empty dependency array if it doesn't depend on component state/props

  const dropdownAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: dropdownScale.value }],
  }));
  const headerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: headerOpacity.value,
  }));
  const backdropAnimatedStyle = useAnimatedStyle(() => ({
    opacity: backdropOpacity.value,
    pointerEvents: isMenuVisible ? 'auto' : 'none',
  }));
  const plusIconAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${plusIconRotation.value}deg` }],
  }));

  const handleProfileSelect = useCallback(
    async (profileId: string) => {
      if (profileId !== selectedProfileId) {
        setSelectedProfileId(profileId);
        setRecommendations(null); // Clear recommendations for new profile
        setCurrentFeedbackMap(new Map()); // Clear feedback map
        fetchRecommendations(profileId);
        fetchProfileFeedback(profileId);
        try {
          await AsyncStorage.setItem(SELECTED_PROFILE_KEY, profileId);
        } catch {
          /* LOW 2: Error saving selected profile */
        }
      }
      setShowDropdown(false);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    },
    [selectedProfileId, fetchRecommendations, fetchProfileFeedback]
  );

  const handleDropdownPress = () => setShowDropdown(true);

  const handleGeneratePress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    if (selectedProfileId) fetchRecommendations(selectedProfileId);
  };

  // handleFeedback remains largely the same but uses processingFeedbackRef (already present)
  // LOW 6: Changed temporary ID to use uuidv4
  const handleFeedback = async (
    item: GiftRecommendation,
    feedbackType: 'like' | 'dislike'
  ) => {
    const recommendationId = item.id;
    if (
      !user?.uid ||
      !selectedProfileId ||
      !recommendationId ||
      typeof recommendationId !== 'string'
    )
      return;
    if (processingFeedbackRef.current.has(recommendationId)) return;

    processingFeedbackRef.current.add(recommendationId);
    const existingFeedback = currentFeedbackMap.get(recommendationId);
    const newFeedbackMap = new Map(currentFeedbackMap);

    try {
      if (existingFeedback?.feedbackType === feedbackType) {
        newFeedbackMap.delete(recommendationId);
        setCurrentFeedbackMap(newFeedbackMap);
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        if (existingFeedback.id)
          await deleteRecommendationFeedback(existingFeedback.id); // Ensure existingFeedback.id is valid
      } else {
        const tempFeedback: FeedbackEntry = {
          id: uuidv4(), // LOW 6: Use uuid for temp client-side ID
          userId: user.uid,
          profileId: selectedProfileId,
          recommendationId,
          feedbackType,
          recommendationDetails: {
            name: item.name,
            description: item.description,
          },
          timestamp: new Date(), // Or Timestamp.now() if FeedbackEntry expects Timestamp
        };
        newFeedbackMap.set(recommendationId, tempFeedback);
        setCurrentFeedbackMap(newFeedbackMap);
        Haptics.impactAsync(
          feedbackType === 'like'
            ? Haptics.ImpactFeedbackStyle.Light
            : Haptics.ImpactFeedbackStyle.Medium
        );

        const success = await saveRecommendationFeedback({
          userId: user.uid,
          profileId: selectedProfileId,
          recommendationId,
          feedbackType,
          recommendationDetails: {
            name: item.name,
            description: item.description,
          },
        });
        if (!success) throw new Error('Save feedback failed');
        await fetchProfileFeedback(selectedProfileId); // Refetch to get actual IDs
      }
    } catch (err) {
      // Revert optimistic update
      if (existingFeedback)
        newFeedbackMap.set(recommendationId, existingFeedback);
      else newFeedbackMap.delete(recommendationId);
      setCurrentFeedbackMap(newFeedbackMap);
      Alert.alert('Error', 'Could not save your feedback. Please try again.');
    } finally {
      processingFeedbackRef.current.delete(recommendationId);
    }
  };

  const handleNavigateToAddProfile = () => {
    router.push('/profiles/add');
    setIsMenuVisible(false);
  };
  const handleNavigateToEditProfile = () => {
    if (selectedProfileId) {
      router.push(`/profiles/${selectedProfileId}/edit`);
    } else {
      Alert.alert('No Profile Selected', 'Please select a profile to edit.'); // LOW 1: User feedback
    }
    setIsMenuVisible(false);
  };
  const handleOpenNoteModal = () => {
    setIsNoteModalVisible(true);
    setIsMenuVisible(false);
  };

  const handleOpenDateModal = () => {
    setIsDateModalVisible(true);
    setIsMenuVisible(false);
  };

  const handleOpenGiftModal = () => {
    setIsGiftModalVisible(true);
    setIsMenuVisible(false);
  };

  const handleSaveNote = async (noteText: string) => {
    if (!user?.uid || !selectedProfileId || !selectedProfile) {
      Alert.alert('Error', 'Cannot save note. Ensure a profile is selected.'); // LOW 1: User feedback
      return;
    }
    try {
      const newNote = { id: uuidv4(), note: noteText, date: Timestamp.now() }; // Use uuid for note ID
      const updatedNotes = [...(selectedProfile.generalNotes || []), newNote];
      await updateSignificantOther(user.uid, selectedProfileId, {
        generalNotes: updatedNotes,
      });
      Alert.alert('Success', 'Note saved successfully!'); // LOW 1: User feedback
      setIsNoteModalVisible(false);
      // Consider refreshing profile data locally if notes affect UI directly here.
      // For now, data will refresh on next focus or full data load.
      const updatedProfile = { ...selectedProfile, generalNotes: updatedNotes };
      setProfiles(
        (prevProfiles) =>
          prevProfiles?.map((p) =>
            p.profileId === selectedProfileId ? updatedProfile : p
          ) || null
      );
    } catch (err) {
      Alert.alert('Error', 'Could not save note. Please try again.'); // LOW 1: User feedback
    }
  };

  const handleSaveDate = async (dateData: {
    name: string;
    date: Date | null;
    profileId: string | null;
  }) => {
    if (!user?.uid || !selectedProfileId || !selectedProfile || !dateData.date) {
      Alert.alert('Error', 'Cannot save date. Ensure a profile is selected and date is provided.');
      return;
    }
    try {
      const newDate = {
        id: uuidv4(),
        name: dateData.name,
        type: 'Custom',
        date: Timestamp.fromDate(dateData.date)
      };
      const updatedDates = [...(selectedProfile.customDates || []), newDate];
      await updateSignificantOther(user.uid, selectedProfileId, {
        customDates: updatedDates,
      });
      Alert.alert('Success', 'Date saved successfully!');
      setIsDateModalVisible(false);

      const updatedProfile = { ...selectedProfile, customDates: updatedDates };
      setProfiles(
        (prevProfiles) =>
          prevProfiles?.map((p) =>
            p.profileId === selectedProfileId ? updatedProfile : p
          ) || null
      );
    } catch (err) {
      Alert.alert('Error', 'Could not save date. Please try again.');
    }
  };

  const handleSaveGift = async (giftData: {
    item: string;
    occasion?: string;
    date: Date | null;
    reaction?: string;
  }) => {
    if (!user?.uid || !selectedProfileId || !selectedProfile) {
      Alert.alert('Error', 'Cannot save gift. Ensure a profile is selected.');
      return;
    }
    try {
      const newGift = {
        item: giftData.item,
        occasion: giftData.occasion,
        date: giftData.date ? Timestamp.fromDate(giftData.date) : Timestamp.now(),
        reaction: giftData.reaction
      };
      const updatedGifts = [...(selectedProfile.pastGiftsGiven || []), newGift];
      await updateSignificantOther(user.uid, selectedProfileId, {
        pastGiftsGiven: updatedGifts,
      });
      Alert.alert('Success', 'Gift saved successfully!');
      setIsGiftModalVisible(false);

      const updatedProfile = { ...selectedProfile, pastGiftsGiven: updatedGifts };
      setProfiles(
        (prevProfiles) =>
          prevProfiles?.map((p) =>
            p.profileId === selectedProfileId ? updatedProfile : p
          ) || null
      );
    } catch (err) {
      Alert.alert('Error', 'Could not save gift. Please try again.');
    }
  };

  const selectedProfile = useMemo(
    () => profiles?.find((p) => p.profileId === selectedProfileId),
    [profiles, selectedProfileId]
  );

  // --- Render Logic ---
  if (loading && !dataInitialized) {
    // Show initial full screen loader only if data not yet initialized
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <View className="items-center justify-center flex-1">
          <Text className="mb-6 text-xl font-semibold text-primary dark:text-primary-dark">
            Loading your profiles...
          </Text>
          <LoadingIndicator
            color={isDark ? '#C70039' : '#A3002B'}
            size="large"
          />
        </View>
      </SafeAreaView>
    );
  }

  if (!profiles || (profiles.length === 0 && !loading)) {
    // Show empty state if no profiles after loading
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <Animated.View
          entering={FadeIn.duration(600)}
          className="items-center justify-center flex-1 px-6"
        >
          <View className="w-full max-w-sm p-8 shadow-md rounded-2xl bg-card dark:bg-card-dark">
            <Text className="mb-3 text-2xl font-bold text-center text-primary dark:text-primary-dark">
              Welcome to Giftmi!
            </Text>
            <Text className="mb-8 text-base text-center text-text-secondary dark:text-text-secondary-dark">
              Create a gift profile to get personalized recommendations
            </Text>
            <Link href="/profiles/add" asChild>
              <Button
                title="Create a Gift Profile"
                variant="primary"
                className="w-full mb-4"
                onPress={() =>
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium)
                }
                leftIcon={
                  <Feather name="plus-circle" size={18} color="white" />
                }
              />
            </Link>

            {error && (
              <Animated.Text
                entering={FadeIn.duration(300)}
                className="mt-6 text-center text-error dark:text-error-dark"
              >
                {error}
              </Animated.Text>
            )}
          </View>
        </Animated.View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-background">
      <View className="relative flex-1 px-5 py-6">
        <Animated.View
          style={backdropAnimatedStyle}
          className="absolute inset-0 z-20 bg-black"
          onTouchEnd={() => setIsMenuVisible(false)}
        />
        <View className="flex-row items-center justify-between mb-8">
          <Animated.View style={headerAnimatedStyle}>
            <Text className="text-2xl font-bold text-primary dark:text-primary-dark">
              Giftmi
            </Text>
          </Animated.View>
          {profiles.length === 1 && selectedProfile ? (
            <View className="p-2 border shadow-sm rounded-xl bg-card dark:bg-card-dark border-border dark:border-border-dark">
              <Text className="text-lg font-semibold text-primary dark:text-primary-dark">
                {selectedProfile.name}
              </Text>
            </View>
          ) : (
            <View className="relative w-1/2">
              <AnimatedPressable
                className="flex-row items-center justify-between p-2 border shadow-sm rounded-xl bg-card dark:bg-card-dark border-border dark:border-border-dark"
                style={[dropdownAnimatedStyle]}
                onPress={handleDropdownPress}
                accessibilityRole="button"
                accessibilityLabel="Select profile"
              >
                <Text className="text-text-secondary dark:text-text-secondary-dark">
                  Profile:{' '}
                </Text>
                <Text className="text-lg font-semibold text-primary dark:text-primary-dark">
                  {selectedProfile?.name || 'Select Profile'}
                </Text>
                <Feather
                  name="chevron-down"
                  size={20}
                  color={isDark ? '#C70039' : '#A3002B'}
                />
              </AnimatedPressable>
              <Modal
                visible={showDropdown}
                transparent
                animationType="fade"
                onRequestClose={() => setShowDropdown(false)}
              >
                <Pressable
                  className="absolute inset-0 bg-black/40"
                  onPress={() => setShowDropdown(false)}
                />
                <Animated.View
                  entering={FadeIn.duration(200)}
                  className="absolute bottom-0 left-0 right-0 shadow-lg rounded-t-2xl bg-card dark:bg-card-dark"
                >
                  <View className="flex items-center pt-3 pb-2">
                    <View className="w-16 h-1 rounded-full bg-border dark:bg-border-dark" />
                  </View>
                  <Text className="px-5 py-3 text-lg font-semibold text-text-primary dark:text-text-primary-dark">
                    Select a profile
                  </Text>
                  {profiles.map((profile) => (
                    <Pressable
                      key={profile.profileId}
                      className={`p-4 border-b border-border dark:border-border-dark ${
                        profile.profileId === selectedProfileId
                          ? 'bg-primary/5 dark:bg-primary-dark/10'
                          : ''
                      }`}
                      onPress={() => handleProfileSelect(profile.profileId)}
                      accessibilityRole="button"
                      accessibilityLabel={`Select ${profile.name}`}
                      accessibilityState={{
                        selected: profile.profileId === selectedProfileId,
                      }}
                    >
                      <Text
                        className={`text-base ${
                          profile.profileId === selectedProfileId
                            ? 'font-semibold text-primary dark:text-primary-dark'
                            : 'text-text-primary dark:text-text-primary-dark'
                        }`}
                      >
                        {profile.name}
                      </Text>
                    </Pressable>
                  ))}
                  <SafeAreaView>
                    <View className="p-5">
                      <Button
                        title="Close"
                        variant="secondary"
                        onPress={() => setShowDropdown(false)}
                        className="w-full"
                      />
                    </View>
                  </SafeAreaView>
                </Animated.View>
              </Modal>
            </View>
          )}
          <TouchableOpacity
            onPress={() => setIsMenuVisible((prev) => !prev)}
            testID="plus-icon-button"
          >
            <Animated.View style={plusIconAnimatedStyle}>
              <Feather
                name="plus"
                size={28}
                color={isDark ? '#C70039' : '#A3002B'}
              />
            </Animated.View>
          </TouchableOpacity>
        </View>

        {isMenuVisible && (
          <ActionMenu
            className="absolute z-40 right-1 top-20"
            onClose={() => setIsMenuVisible(false)}
            onAddProfile={handleNavigateToAddProfile}
            onAddNote={handleOpenNoteModal}
            onAddDate={handleOpenDateModal}
            onAddGift={handleOpenGiftModal}
            onEditProfile={handleNavigateToEditProfile}
          />
        )}

        {feedbackError && (
          <Animated.View
            entering={FadeIn.duration(300)}
            className="p-3 mb-4 rounded-md bg-error/20 dark:bg-error-dark/20"
          >
            <Text className="text-sm text-center text-error dark:text-error-dark">
              {feedbackError}
            </Text>
          </Animated.View>
        )}

        <ScrollView
          className="flex-1"
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 20 }}
        >
          {/* Header Content */}
          <View className="px-0">
            {/* Motivational Header */}
            <MotivationalHeader userName={user?.displayName || undefined} />

            {/* Profile Completion Banner */}
            <ProfileCompletionBanner profile={selectedProfile || null} />

            {/* Navigation Grid */}
            <NavigationGrid selectedProfileId={selectedProfileId} />

            {/* Key Dates Display */}
            <KeyDatesDisplay profile={selectedProfile || null} />

            {/* Upcoming Dates Display */}
            {selectedProfileId && (
              <UpcomingDatesDisplay
                upcomingDates={upcomingDates}
                onAddDatePress={handleOpenDateModal}
              />
            )}
          </View>

          {/* Recommendations Section */}
          <View className="w-full max-w-sm mx-auto">
            {/* Recommendations Header */}
            <View className="flex-row items-center justify-between mb-4">
              <Text className="text-xl font-bold text-text-primary dark:text-text-primary-dark">
                Daily Gift Recommendations
              </Text>
              {recommendations && recommendations.length > 0 && recommendationsLoading && (
                <View className="flex-row items-center">
                  <LoadingIndicator color={'#A3002B'} size="small" />
                  <Text className="ml-2 text-sm text-text-secondary dark:text-text-secondary-dark">
                    Updating...
                  </Text>
                </View>
              )}
            </View>

            {recommendationsLoading && !recommendations?.length ? ( // Show loader if loading AND no recommendations yet
              <View className="items-center justify-center py-16">
                <LoadingIndicator color={'#A3002B'} size="large" />
                <Text className="mt-6 text-base font-medium text-text-secondary dark:text-text-secondary-dark">
                  Finding perfect gifts...
                </Text>
              </View>
            ) : recommendationsError ? (
              <Animated.View
                entering={FadeIn.duration(300)}
                className="items-center justify-center py-16"
              >
                <View className="items-center justify-center w-20 h-20 mb-6 rounded-full bg-error/10 dark:bg-error-dark/10">
                  <Feather
                    name="alert-circle"
                    size={32}
                    color={isDark ? '#B20021' : '#D90429'}
                  />
                </View>
                <Text className="mb-6 text-lg font-medium text-center text-error dark:text-error-dark">
                  {recommendationsError}
                </Text>
                <Button
                  title="Try Again"
                  variant="secondary"
                  className="px-6"
                  onPress={handleGeneratePress}
                />
              </Animated.View>
            ) : recommendations && recommendations.length > 0 ? (
              <View>
                <Animated.View
                  className={`${recommendationsLoading ? 'opacity-60' : 'opacity-100'}`}
                >
                  {recommendations.map((item, index) => (
                    <AnimatedCard
                      key={item.id}
                      entering={SlideInRight.delay(index * 100).springify()}
                      className="mb-4 overflow-hidden border shadow-sm rounded-xl bg-card dark:bg-card-dark border-border dark:border-border-dark"
                    >
                    <View className="p-5">
                      <Text className="text-lg font-semibold text-primary dark:text-primary-dark">
                        {item.name}
                      </Text>
                      <Text className="mt-2 text-base text-text-secondary dark:text-text-secondary-dark">
                        {item.description}
                      </Text>
                      <View className="flex-row flex-wrap mt-4">
                        {item.priceRange && (
                          <View className="px-3 py-1 mb-2 mr-2 rounded-full bg-primary/10 dark:bg-primary-dark/10">
                            <Text className="text-xs font-medium text-primary dark:text-primary-dark">
                              {item.priceRange}
                            </Text>
                          </View>
                        )}
                        {item.categories?.map((category: string, i: number) => (
                          <View
                            key={i}
                            className="px-3 py-1 mb-2 mr-2 rounded-full bg-accent/10 dark:bg-accent-dark/10"
                          >
                            <Text className="text-xs font-medium text-accent dark:text-accent-dark">
                              {category}
                            </Text>
                          </View>
                        ))}
                      </View>
                      <View className="flex-row items-center justify-between pt-3 mt-3 border-t border-border/50 dark:border-border-dark/50">
                        <Pressable
                          className="p-3 rounded-full active:bg-error/10"
                          onPress={() => handleFeedback(item, 'dislike')}
                          accessibilityLabel="Dislike this recommendation"
                          accessibilityRole="button"
                          disabled={processingFeedbackRef.current.has(item.id)}
                          accessibilityState={{
                            selected:
                              currentFeedbackMap.get(item.id)?.feedbackType ===
                              'dislike',
                          }}
                        >
                          <MaterialCommunityIcons
                            name={
                              currentFeedbackMap.get(item.id)?.feedbackType ===
                              'dislike'
                                ? 'thumb-down'
                                : 'thumb-down-outline'
                            }
                            size={22}
                            color={isDark ? '#B20021' : '#D90429'}
                          />
                        </Pressable>
                        <Pressable
                          className="p-3 rounded-full active:bg-primary/10"
                          onPress={() => handleFeedback(item, 'like')}
                          accessibilityLabel="Like this recommendation"
                          accessibilityRole="button"
                          disabled={processingFeedbackRef.current.has(item.id)}
                          accessibilityState={{
                            selected:
                              currentFeedbackMap.get(item.id)?.feedbackType ===
                              'like',
                          }}
                        >
                          <MaterialCommunityIcons
                            name={
                              currentFeedbackMap.get(item.id)?.feedbackType ===
                              'like'
                                ? 'thumb-up'
                                : 'thumb-up-outline'
                            }
                            size={22}
                            color={isDark ? '#16A34A' : '#22C55E'}
                          />
                        </Pressable>
                      </View>
                    </View>
                  </AnimatedCard>
                ))}
                </Animated.View>
                {selectedProfileId && (
                  <Button
                    title={
                      recommendationsLoading
                        ? 'Finding Gifts...'
                        : 'Generate New Ideas'
                    }
                    variant="primary"
                    className="w-full mt-5"
                    onPress={handleGeneratePress}
                    disabled={!selectedProfileId || recommendationsLoading}
                    isLoading={recommendationsLoading}
                  />
                )}
              </View>
            ) : (
              <View className="items-center justify-center py-16">
                <View className="items-center justify-center w-20 h-20 mb-6 rounded-full bg-primary/10 dark:bg-primary-dark/10">
                  <Feather
                    name="gift"
                    size={32}
                    color={isDark ? '#C70039' : '#A3002B'}
                  />
                </View>
                <Text className="text-lg text-center text-text-secondary dark:text-text-secondary-dark">
                  {selectedProfileId
                    ? 'No recommendations yet. Generate some gift ideas!'
                    : 'Select a profile to see recommendations'}
                </Text>
                {feedbackError && (
                  <Text className="mt-4 text-sm text-center text-error dark:text-error-dark">
                    {feedbackError}
                  </Text>
                )}
                {selectedProfileId && (
                  <Button
                    title={
                      recommendationsLoading
                        ? 'Finding Gifts...'
                        : 'Generate New Ideas'
                    }
                    variant="primary"
                    className="w-full mt-6"
                    onPress={handleGeneratePress}
                    disabled={!selectedProfileId || recommendationsLoading}
                    isLoading={recommendationsLoading}
                  />
                )}
              </View>
            )}
          </View>
        </ScrollView>
      </View>
      <View>
        <AddGeneralNoteModal
          isVisible={isNoteModalVisible}
          onClose={() => setIsNoteModalVisible(false)}
          onSave={handleSaveNote}
        />
        <AddCustomDateModal
          isVisible={isDateModalVisible}
          onClose={() => setIsDateModalVisible(false)}
          onAddItem={handleSaveDate}
          profileId={selectedProfileId}
        />
        <AddPastGiftModal
          isVisible={isGiftModalVisible}
          onClose={() => setIsGiftModalVisible(false)}
          onAddItem={handleSaveGift}
        />
      </View>
    </SafeAreaView>
  );
}
