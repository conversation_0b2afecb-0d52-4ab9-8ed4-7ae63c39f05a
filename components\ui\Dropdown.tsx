import React from 'react';
import { View, Text } from 'react-native';
import { Picker } from '@react-native-picker/picker';

interface DropdownProps {
  options: { label: string; value: string | number }[];
  selectedValue?: string | number;
  onValueChange: (value: string | number, index: number) => void;
  placeholder?: string;
  label?: string;
  error?: string;
  accessibilityLabel?: string;
}

const Dropdown: React.FC<DropdownProps> = ({
  options,
  selectedValue,
  onValueChange,
  placeholder,
  label,
  error,
  accessibilityLabel,
}) => {
  const baseContainerClasses = 'mb-4';
  const baseLabelClasses = 'text-sm font-medium mb-1 text-text-secondary';
  const baseErrorClasses = 'text-sm text-feedback-error mt-1';
  const pickerClasses = 'border border-border bg-input-background text-text-primary rounded-md';

  return (
    <View className={baseContainerClasses}>
      {label && (
        <Text className={baseLabelClasses}>
          {label}
        </Text>
      )}
      <View className={pickerClasses}>
        <Picker
          selectedValue={selectedValue}
          onValueChange={onValueChange}
          accessibilityLabel={accessibilityLabel}
          dropdownIconColor="text-secondary"
        >
          {placeholder && (
            <Picker.Item 
              label={placeholder} 
              value={null} 
              enabled={false}
            />
          )}
          {options.map((option) => (
            <Picker.Item
              key={option.value}
              label={option.label}
              value={option.value}
            />
          ))}
        </Picker>
      </View>
      {error && (
        <Text className={baseErrorClasses}>
          {error}
        </Text>
      )}
    </View>
  );
};

export default Dropdown;