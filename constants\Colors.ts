// constants/Colors.ts

/**
 * Giftmi Application Color Palette (Pomegranate Theme).
 *
 * This file centralizes the color definitions from `tailwind.config.js`
 * for programmatic use within the application, especially for components
 * like navigation where direct hex values are required.
 *
 * It ensures consistency with the NativeWind styling and supports
 * light and dark mode theming as defined in the style guide.
 */

// --- Pomegranate Core Colors (from tailwind.config.js) ---
const primary = {
  DEFAULT: '#A3002B', // Rich pomegranate red
  '500': '#A3002B',   // Rich pomegranate red
  '600': '#800022',   // Darker pomegranate red
  dark: '#C70039',  // Slightly brighter, more vivid red for dark mode emphasis
};

const accent = {
  DEFAULT: '#E5355F', // Ruby/pinkish-red aril color
  '500': '#E5355F',   // Ruby/pinkish-red aril color
  '600': '#C22A4F',   // Darker ruby red
  dark: '#FF507B',  // More vibrant pinkish-red for dark mode accent
};

// --- UI Element Colors (Pomegranate Inspired - Light Mode) ---
const lightUI = {
  background: '#FFFBF7',    // Very light, warm off-white (like pomegranate pith)
  card: '#FFFEFD',        // Almost white, but warm (pith variation)
  textPrimary: '#3D1C26',  // Dark, slightly warm grey/brown
  textSecondary: '#7A3E4F',// Medium, warm grey/brown
  border: '#EED9DF',      // Light, desaturated pinkish grey
  inputBackground: '#FFFEFD', // Same as card light
  disabled: '#D1C5C9',    // Desaturated pinkish grey
};

// --- UI Element Colors (Pomegranate Inspired - Dark Mode) ---
const darkUI = {
  background: '#1F1216',    // Very dark, desaturated red/brown
  card: '#2F1E23',        // Dark grey with a hint of red
  textPrimary: '#FCEBEF',  // Light, slightly pinkish off-white
  textSecondary: '#D7B8C0',// Lighter, desaturated pinkish grey
  border: '#4D2A36',      // Darker, desaturated reddish brown
  inputBackground: '#2F1E23', // Same as card dark
  disabled: '#786A6E',    // Darker desaturated pinkish grey
};

// --- Utility/Feedback Colors (Common for Light/Dark, but can be specific) ---
const feedback = {
  error: {
    DEFAULT: '#D90429',
    dark: '#B20021',
  },
  success: {
    DEFAULT: '#22C55E', // Kept standard green
    dark: '#16A34A',   // Kept standard green
  },
};

// --- Semantic Colors (Pomegranate Tinted) ---
const semanticColors = {
  holiday: {
    DEFAULT: '#C70039', // Using primary.dark for a strong holiday red
    dark: '#A3002B',    // Using primary.DEFAULT
  },
  birthday: {
    DEFAULT: '#E5355F', // Using accent.DEFAULT
    dark: '#C22A4F',    // Using accent.600
  },
  anniversary: {
    DEFAULT: '#5C002E', // Deep magenta/purple
    dark: '#4A0025',
  },
  customDate: {
    DEFAULT: '#7D003F', // A distinct pomegranate-esque purple/magenta
    dark: '#620031',
  },
};

// Assembling the final export structure for easier consumption.
export const colors = {
  light: {
    primary: primary.DEFAULT,
    primaryStrong: primary['600'],
    accent: accent.DEFAULT,
    accentStrong: accent['600'],
    background: lightUI.background,
    card: lightUI.card,
    textPrimary: lightUI.textPrimary,
    textSecondary: lightUI.textSecondary,
    border: lightUI.border,
    inputBackground: lightUI.inputBackground,
    disabled: lightUI.disabled,
    error: feedback.error.DEFAULT,
    success: feedback.success.DEFAULT,
    holiday: semanticColors.holiday.DEFAULT,
    birthday: semanticColors.birthday.DEFAULT,
    anniversary: semanticColors.anniversary.DEFAULT,
    customDate: semanticColors.customDate.DEFAULT,
  },
  dark: {
    primary: primary.dark,
    primaryStrong: primary['600'], // Using the general darker shade for strong variant in dark mode too
    accent: accent.dark,
    accentStrong: accent['600'], // Using the general darker shade
    background: darkUI.background,
    card: darkUI.card,
    textPrimary: darkUI.textPrimary,
    textSecondary: darkUI.textSecondary,
    border: darkUI.border,
    inputBackground: darkUI.inputBackground,
    disabled: darkUI.disabled,
    error: feedback.error.dark,
    success: feedback.success.dark,
    holiday: semanticColors.holiday.dark,
    birthday: semanticColors.birthday.dark,
    anniversary: semanticColors.anniversary.dark,
    customDate: semanticColors.customDate.dark,
  },

  // Direct access to color groups matching tailwind.config.js structure where needed
  primary: {
    DEFAULT: primary.DEFAULT,
    '500': primary['500'],
    '600': primary['600'],
    dark: primary.dark,
  },
  accent: {
    DEFAULT: accent.DEFAULT,
    '500': accent['500'],
    '600': accent['600'],
    dark: accent.dark,
  },
  background: { // Added for completeness if direct access to background is needed
    DEFAULT: lightUI.background,
    dark: darkUI.background,
  },
  card: {
    DEFAULT: lightUI.card,
    dark: darkUI.card,
  },
  'text-primary': {
    DEFAULT: lightUI.textPrimary,
    dark: darkUI.textPrimary,
  },
  'text-secondary': {
    DEFAULT: lightUI.textSecondary,
    dark: darkUI.textSecondary,
  },
  border: {
    DEFAULT: lightUI.border,
    dark: darkUI.border,
  },
  'input-background': { // Added for completeness
    DEFAULT: lightUI.inputBackground,
    dark: darkUI.inputBackground,
  },
  disabled: { // Added for completeness
    DEFAULT: lightUI.disabled,
    dark: darkUI.disabled,
  },
  error: { // Added for completeness
    DEFAULT: feedback.error.DEFAULT,
    dark: feedback.error.dark,
  },
  success: { // Added for completeness
    DEFAULT: feedback.success.DEFAULT,
    dark: feedback.success.dark,
  },
  // Semantic colors direct access
  holiday: {
    DEFAULT: semanticColors.holiday.DEFAULT,
    dark: semanticColors.holiday.dark,
  },
  birthday: {
    DEFAULT: semanticColors.birthday.DEFAULT,
    dark: semanticColors.birthday.dark,
  },
  anniversary: {
    DEFAULT: semanticColors.anniversary.DEFAULT,
    dark: semanticColors.anniversary.dark,
  },
  customDate: {
    DEFAULT: semanticColors.customDate.DEFAULT,
    dark: semanticColors.customDate.dark,
  },
};

export default colors;