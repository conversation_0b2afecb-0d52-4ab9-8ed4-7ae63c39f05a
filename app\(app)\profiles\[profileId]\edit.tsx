import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Platform, Alert, ActivityIndicator } from 'react-native';
import { useLocalSearchPara<PERSON>, Stack, useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';
import * as Haptics from 'expo-haptics';
import AsyncStorage from '@react-native-async-storage/async-storage'; // Import AsyncStorage
import { useForm, SubmitHandler } from 'react-hook-form';
import { PROFILES_LAST_UPDATED_KEY } from '@/constants/storageKeys'; // Import centralized constant
// Removed Feather import (not used here)

import { useAuth } from '@/contexts/AuthContext';
import { getSignificantOtherById, updateSignificantOther, deleteSignificantOther } from '@/services/profileService';
// Removed feedback service imports
import { SignificantOtherProfile } from '@/functions/src/types/firestore'; // Removed list item types
import { Timestamp } from 'firebase/firestore';
import { format } from 'date-fns'; // Added for date formatting
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import LoadingIndicator from '@/components/ui/LoadingIndicator';
import ProfileForm, { ProfileFormData } from '@/components/profile/ProfileForm';

import { formatDateForDisplay } from '@/utils/dateUtils'; // Import centralized date formatter

// Removed local formatDate helper function


// Renamed component
const ProfileEditScreen = () => {
  const { profileId } = useLocalSearchParams<{ profileId: string }>();
  const { user } = useAuth();
  const [profile, setProfile] = useState<SignificantOtherProfile | null>(null); // Keep profile state for initial data load
  const [isLoading, setIsLoading] = useState<boolean>(true); // Loading state for initial fetch and submit
  const [error, setError] = useState<string | null>(null); // Error state for fetch/submit
  // Removed isEditing state
  // Removed feedback state
  const [isDatePickerVisible, setDatePickerVisibility] = useState(false);
  const [datePickerField, setDatePickerField] = useState<'birthday' | 'anniversary' | null>(null);
  const router = useRouter();

  const { control, handleSubmit, setValue, reset, watch, formState: { errors } } = useForm<ProfileFormData>({
    mode: 'onChange',
  });

  // Fetch initial profile data to populate the form
  useEffect(() => {
    const fetchProfileData = async () => {
      if (!user?.uid || !profileId) {
        setError('User or Profile ID missing.');
        setIsLoading(false);
        return;
      }
      setIsLoading(true);
      setError(null);
      try {
        const fetchedProfile = await getSignificantOtherById(user.uid, profileId);
        if (fetchedProfile) {
          setProfile(fetchedProfile); // Store fetched profile
          // Populate form using reset
          const formData: ProfileFormData = {
            name: fetchedProfile.name || '',
            relationship: fetchedProfile.relationship || '',
            birthday: fetchedProfile.birthday ? fetchedProfile.birthday.toDate() : null, // Keep as Date for schema
            anniversary: fetchedProfile.anniversary ? fetchedProfile.anniversary.toDate() : null, // Keep as Date for schema
            interestsInput: fetchedProfile.interests?.join(', ') || '',
            dislikesInput: fetchedProfile.dislikes?.join(', ') || '',
            preferences: { // Nest preferences here
              favoriteColor: fetchedProfile.preferences?.favoriteColor || '',
              preferredStyle: fetchedProfile.preferences?.preferredStyle || '',
              favoriteBrands: fetchedProfile.preferences?.favoriteBrands || [], // Assign the array directly, or an empty array
            },
            clothingSize: fetchedProfile.sizes?.clothing || '',
            shoeSize: fetchedProfile.sizes?.shoe || '',
            wishlistItems: fetchedProfile.wishlistItems?.map(item => ({
              ...item,
              dateAdded: item.dateAdded && typeof item.dateAdded.toDate === 'function' ? item.dateAdded.toDate() : null, // Convert to Date | null
            })) || [],
            pastGiftsGiven: fetchedProfile.pastGiftsGiven?.map(gift => ({
              item: gift.item,
              occasion: gift.occasion || '',
              date: gift.date && typeof gift.date.toDate === 'function' ? gift.date.toDate() : null, // Convert to Date | null
              reaction: gift.reaction || ''
            })) || [],
            generalNotes: fetchedProfile.generalNotes?.map(note => ({
              note: note.note,
              date: note.date && typeof note.date.toDate === 'function' ? note.date.toDate() : null, // Convert to Date | null
            })) || [],
            // Convert customDates from Timestamp to Date for form state
            customDates: fetchedProfile.customDates?.map(dateItem => ({
              id: dateItem.id,
              name: dateItem.name,
              date: dateItem.date ? dateItem.date.toDate() : null, // Convert Timestamp to Date, handle null
            })) || [],
          };
          reset(formData);
        } else {
          setError('Profile not found or access denied.');
        }
      } catch (err) {
        console.error('Error fetching profile for edit:', err); // Kept console.error for now
        setError('Failed to load profile data. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };
    fetchProfileData();
  }, [profileId, user?.uid]); // Removed 'reset' dependency

  // onSubmit handler remains largely the same
  const onSubmit: SubmitHandler<ProfileFormData> = useCallback(async (data) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    if (!user) {
      setError("You must be logged in to update a profile.");
      return;
    }
    if (!profileId) {
      setError("Profile ID is missing.");
      return;
    }

    setError(null);
    setIsLoading(true);

    try {
      const profileData: Partial<SignificantOtherProfile> = {
        name: data.name.trim(),
        relationship: data.relationship.trim(),
        birthday: data.birthday ? Timestamp.fromDate(data.birthday) : null,
        anniversary: data.anniversary ? Timestamp.fromDate(data.anniversary) : null,
        // Generate MonthDay fields
        birthdayMonthDay: data.birthday instanceof Date ? format(data.birthday, 'MM-dd') : undefined,
        anniversaryMonthDay: data.anniversary instanceof Date ? format(data.anniversary, 'MM-dd') : undefined,
        interests: data.interestsInput ? data.interestsInput.split(',').map(i => i.trim()).filter(Boolean) : [],
        dislikes: data.dislikesInput ? data.dislikesInput.split(',').map(i => i.trim()).filter(Boolean) : [],
        preferences: {
          favoriteColor: data.preferences?.favoriteColor?.trim() || null,
          preferredStyle: data.preferences?.preferredStyle?.trim() || null,
          favoriteBrands: data.preferences?.favoriteBrands ? data.preferences.favoriteBrands.map((i: string) => i.trim()).filter(Boolean) : [],
        },
        sizes: {
          clothing: data.clothingSize?.trim() || null,
          shoe: data.shoeSize?.trim() || null,
        },
        wishlistItems: data.wishlistItems?.map(item => ({
          ...item,
          // Convert Date | null from form to Timestamp | null for Firestore
          dateAdded: item.dateAdded instanceof Date ? Timestamp.fromDate(item.dateAdded) : null
        })) || [],
        pastGiftsGiven: data.pastGiftsGiven?.map(gift => ({
          item: gift.item.trim(),
          occasion: gift.occasion?.trim() || '',
          // Convert Date | null from form to Timestamp | null for Firestore
          date: gift.date instanceof Date ? Timestamp.fromDate(gift.date) : null,
          reaction: gift.reaction?.trim() || ''
        })) || [],
        generalNotes: data.generalNotes?.map(note => ({
          note: note.note.trim(),
          // Convert Date | null from form to Timestamp | null for Firestore
          date: note.date instanceof Date ? Timestamp.fromDate(note.date) : null,
        })) || [],
        // Convert customDates to Timestamp and add customDateMonthDay before sending to Firestore
        customDates: data.customDates?.map(dateItem => ({
          id: dateItem.id,
          name: dateItem.name.trim(),
          date: dateItem.date ? Timestamp.fromDate(dateItem.date) : null, // Convert Date to Timestamp, handle null
          customDateMonthDay: dateItem.date ? format(dateItem.date, 'MM-dd') : undefined,
        })) || [],
      };

      await updateSignificantOther(user.uid, profileId, profileData);

      // Update the timestamp in AsyncStorage after successful profile update
      await AsyncStorage.setItem(PROFILES_LAST_UPDATED_KEY, Date.now().toString());
      console.log('EDIT PROFILE: Updated profilesLastUpdated timestamp in AsyncStorage after update');

      router.back(); // Go back to the view screen on success

    } catch (err) {
      console.error("EDIT PROFILE: Failed to update profile:", err); // Kept console.error for now
      setError("Failed to update profile. Please try again.");
      // Removed feedback submission
    } finally {
      setIsLoading(false);
    }
  }, [profileId, user, router, setError, setIsLoading]); // Added dependencies

  // Watch date values for date picker UI
  const birthdayValue = watch('birthday');
  const anniversaryValue = watch('anniversary');

  // Date picker handlers remain the same
  const showDatePicker = (field: 'birthday' | 'anniversary') => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setDatePickerField(field);
    setDatePickerVisibility(true);
  };

  const handleDateConfirm = (event: DateTimePickerEvent, selectedDate?: Date) => {
    setDatePickerVisibility(false);
    if (event.type === 'set' && selectedDate && datePickerField) {
      setValue(datePickerField, selectedDate, { shouldValidate: true });
    }
    setDatePickerField(null);
  };

  // Delete profile handler remains the same
  const handleDeleteProfile = async () => {
    if (!user?.uid || !profileId) {
      console.error("Missing user ID or profile ID");
      return;
    }
    Alert.alert(
      "Delete Profile",
      "Are you sure you want to delete this profile? This action cannot be undone.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            setIsLoading(true);
            try {
              await deleteSignificantOther(user.uid, profileId);

              // Update the timestamp in AsyncStorage after successful profile deletion
              await AsyncStorage.setItem(PROFILES_LAST_UPDATED_KEY, Date.now().toString());
              console.log('EDIT PROFILE: Updated profilesLastUpdated timestamp in AsyncStorage after deletion');

              // Navigate back two steps: from edit -> view -> profiles list
              if (router.canGoBack()) {
                 router.back(); // Go back from edit to view
                 if (router.canGoBack()) {
                    router.back(); // Go back from view to list
                  } else {
                    router.replace('/(app)/profiles'); // Fallback if history is shallow
                  }
              } else {
                 router.replace('/(app)/profiles'); // Fallback if cannot go back at all
              }
            } catch (error) {
              console.error("Error deleting profile:", error); // Kept console.error for now
              setError("Failed to delete profile. Please try again.");
              setIsLoading(false); // Ensure loading stops on error
            }
            // No finally here, loading stops on error or navigation happens
          },
        },
      ],
      { cancelable: false }
    );
  };

  // Removed feedback delete handler

  return (
    <SafeAreaView className="flex-1 bg-background">
      <Stack.Screen
        options={{
          title: profile ? `Edit ${profile.name}` : 'Edit Profile',
          // No headerRight needed here, actions are in the form
        }}
      />

      <ScrollView contentContainerClassName="p-4">
        {/* Show loading indicator during initial fetch */}
        {isLoading && !profile && <LoadingIndicator />}

        {error && (
          <View className="items-center justify-center p-4 mb-4 rounded-md bg-destructive">
            <Text className="text-destructive-foreground">{error}</Text>
          </View>
        )}

        {/* --- Editing Form View --- */}
        {/* Render form only after initial data load attempt (even if error occurred) */}
        {!isLoading && profile && (
          <Card className="w-full">
            <View className="mb-6">
              <Text className="text-2xl font-bold text-center text-foreground">Edit Profile</Text>
              <Text className="text-center text-muted-foreground">Update {profile.name}'s details below.</Text>
            </View>

            <ProfileForm
              control={control}
              errors={errors}
              showDatePicker={showDatePicker}
              birthdayValue={birthdayValue}
              anniversaryValue={anniversaryValue}
            />

            {/* Submit Button */}
            <View className="mt-6">
              <Button onPress={handleSubmit(onSubmit)} title="Update Profile" disabled={isLoading}>
                {isLoading ? (
                  <ActivityIndicator size="small" color="#FFFFFF" />
                ) : (
                  'Update Profile'
                )
}</Button>
            </View>

            {/* Date Picker Modal */}
            {isDatePickerVisible && (
              <DateTimePicker
                value={datePickerField === 'birthday' ? birthdayValue || new Date() : anniversaryValue || new Date()}
                mode="date"
                display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                onChange={handleDateConfirm}
              />
            )}
          </Card>
        )}

        {/* Delete Button */}
        {/* Render delete button only after initial data load attempt */}
        {!isLoading && profile && (
          <Button
            title="Delete Profile"
            onPress={handleDeleteProfile}
            className="mt-6 bg-red-600 hover:bg-red-700" // Destructive styling
            accessibilityLabel="Delete profile button"
            disabled={isLoading} // Disable while submitting update
          />
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default ProfileEditScreen;
