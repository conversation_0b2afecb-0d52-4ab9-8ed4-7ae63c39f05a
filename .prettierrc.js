// .prettierrc.js
module.exports = {
  printWidth: 80, // Max line length
  tabWidth: 2, // Spaces per tab
  useTabs: false, // Use spaces instead of tabs
  semi: true, // Add semicolons at the end of statements
  singleQuote: true, // Use single quotes instead of double quotes
  trailingComma: 'es5', // Add trailing commas where valid in ES5 (objects, arrays, etc.)
  bracketSpacing: true, // Add spaces between brackets in object literals
  jsxBracketSameLine: false, // Put the `>` of a multi-line JSX element at the end of the last line
  arrowParens: 'always', // Always include parens around arrow function parameters
  // Optional: Add plugin for Tailwind CSS class sorting if desired later
  // plugins: [require('prettier-plugin-tailwindcss')],
};