// .eslintrc.js
module.exports = {
  root: true, // Prevent ESLint from looking further up the directory tree
  extends: [
    'expo', // Base Expo configuration (includes React, React Native, Jest rules)
    'eslint:recommended', // Basic ESLint recommended rules
    'plugin:@typescript-eslint/recommended', // Recommended TypeScript rules
    'prettier', // Turns off ESLint rules that might conflict with <PERSON>tti<PERSON>
  ],
  plugins: [
    '@typescript-eslint', // Plugin for TypeScript specific rules
    'prettier', // Runs Prettier as an ESLint rule and reports differences as issues
  ],
  parser: '@typescript-eslint/parser', // Specifies the ESLint parser for TypeScript
  parserOptions: {
    ecmaVersion: 2021, // Allows for the parsing of modern ECMAScript features
    sourceType: 'module', // Allows for the use of imports
    ecmaFeatures: {
      jsx: true, // Allows for the parsing of JSX
    },
  },
  rules: {
    'prettier/prettier': 'warn', // Report Prettier differences as warnings
    // Add any project-specific rule overrides here
    // Example: '@typescript-eslint/no-unused-vars': ['warn', { 'argsIgnorePattern': '^_' }],
    // Example: 'react/prop-types': 'off', // Often off in TypeScript projects
    'react/react-in-jsx-scope': 'off', // Not needed with newer React/JSX transform
  },
  settings: {
    react: {
      version: 'detect', // Automatically detect the React version
    },
  },
  env: {
    node: true, // Enable Node.js global variables and Node.js scoping.
    jest: true, // Add Jest global variables
  },
  ignorePatterns: [
    'node_modules/',
    '.expo/',
    'babel.config.js',
    'metro.config.js',
    // Add other files/directories to ignore if needed
  ],
};