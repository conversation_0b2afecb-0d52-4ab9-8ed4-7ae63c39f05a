// Test file to verify our new components work
import React from 'react';
import { View, Text } from 'react-native';
import MotivationalHeader from './components/home/<USER>';
import NavigationGrid from './components/home/<USER>';
import CountdownDisplay from './components/calendar/CountdownDisplay';

const TestComponents = () => {
  const mockClosestDate = {
    id: 'test-1',
    name: 'Birthday',
    type: 'Birthday' as const,
    date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    daysUntil: 7,
    profileId: 'test-profile'
  };

  return (
    <View style={{ flex: 1, padding: 20 }}>
      <Text style={{ fontSize: 24, fontWeight: 'bold', marginBottom: 20 }}>
        Component Tests
      </Text>
      
      <MotivationalHeader userName="Test User" />
      
      <NavigationGrid selectedProfileId="test-profile-id" />
      
      <CountdownDisplay closestDate={mockClosestDate} />
    </View>
  );
};

export default TestComponents;
