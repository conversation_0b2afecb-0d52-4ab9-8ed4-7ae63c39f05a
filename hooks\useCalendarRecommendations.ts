import { useState, useEffect, useCallback } from 'react';
import { fetchGiftRecommendations } from '@/services/recommendationService';
import { CalendarEvent } from './useCalendarData'; // Import CalendarEvent type
import { format } from 'date-fns'; // Import format

const useCalendarRecommendations = (selectedEventForIdeas: CalendarEvent | null, profileId: string | null) => {
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [isLoadingRecommendations, setIsLoadingRecommendations] = useState(false);
  const [recommendationError, setRecommendationError] = useState<string | null>(null);

  const fetchRecommendations = useCallback(async () => {
    if (!selectedEventForIdeas || !profileId) { // Ensure profileId is available
      setRecommendations([]);
      setIsLoadingRecommendations(false);
      setRecommendationError(null);
      return;
    }

    setIsLoadingRecommendations(true);
    setRecommendationError(null);
    setRecommendations([]); // Clear previous recommendations

    try {
      // Call fetchGiftRecommendations with separate arguments
      const result = await fetchGiftRecommendations(
        profileId, // Use the profileId passed to the hook
        selectedEventForIdeas.name, // Use event name as occasion
        format(selectedEventForIdeas.date, 'yyyy-MM-dd') // Format date as string
      );
      setRecommendations(result);
    } catch (err) {
      console.error('Error fetching recommendations:', err);
      setRecommendationError('Failed to fetch gift recommendations.');
      setRecommendations([]);
    } finally {
      setIsLoadingRecommendations(false);
    }
  }, [selectedEventForIdeas, profileId]); // Re-run effect if selectedEventForIdeas or profileId changes

  useEffect(() => {
    fetchRecommendations();
  }, [fetchRecommendations]); // Re-run effect when fetchRecommendations changes (due to selectedEventForIdeas change)

  return {
    recommendations,
    isLoadingRecommendations,
    recommendationError,
    fetchRecommendations, // Expose if manual refresh is needed
  };
};

export default useCalendarRecommendations;