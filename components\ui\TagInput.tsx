import React, { useState, useRef, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, Keyboard, Animated } from 'react-native';
import { Feather } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

interface TagInputProps {
  label?: string;
  tags: string[];
  onChangeTags: (newTags: string[]) => void;
  placeholder?: string;
  error?: string;
  accessibilityLabel?: string;
  maxTags?: number;
  suggestions?: string[];
}

export const TagInput: React.FC<TagInputProps> = ({
  label,
  tags,
  onChangeTags,
  placeholder = 'Add a tag...',
  error,
  accessibilityLabel = 'Tag input',
  maxTags = 50,
  suggestions,
}) => {
  const [inputValue, setInputValue] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [filteredSuggestions, setFilteredSuggestions] = useState<string[]>([]);
  const inputRef = useRef<TextInput>(null);
  const tagAnimations = useRef<{ [key: string]: Animated.Value }>({});

  // Initialize animations for existing tags
  useEffect(() => {
    tags.forEach((tag) => {
      if (!tagAnimations.current[tag]) {
        tagAnimations.current[tag] = new Animated.Value(0);
        Animated.spring(tagAnimations.current[tag], {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }).start();
      }
    });
  }, [tags]);

  const handleAddTag = (value: string) => {
    const values = value.split(',')
      .map(v => v.trim())
      .filter(v => v.length > 0 && !tags.includes(v));
    
    if (values.length > 0 && tags.length < maxTags) {
      const newTags = [...tags, ...values];
      onChangeTags(newTags);
      setInputValue('');
      
      // Create animations for new tags
      values.forEach(tag => {
        tagAnimations.current[tag] = new Animated.Value(0);
        Animated.spring(tagAnimations.current[tag], {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }).start();
      });
      
      // Provide haptic feedback when tag is added
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  const handleInputChange = (text: string) => {
    setInputValue(text);
    if (text.endsWith(',')) {
      handleAddTag(text.slice(0, -1));
    }

    // Filter suggestions
    if (suggestions && text.length > 0) {
      const filtered = suggestions.filter((suggestion: string) =>
        suggestion.toLowerCase().includes(text.toLowerCase()) && !tags.includes(suggestion)
      );
      setFilteredSuggestions(filtered);
    } else {
      setFilteredSuggestions([]);
    }
  };

  const handleKeyPress = (e: any) => {
    if (e.nativeEvent.key === 'Enter') {
      handleAddTag(inputValue);
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    // Animate tag removal
    Animated.timing(tagAnimations.current[tagToRemove], {
      toValue: 0,
      duration: 150,
      useNativeDriver: true,
    }).start(() => {
      onChangeTags(tags.filter(tag => tag !== tagToRemove));
      // Provide haptic feedback when tag is removed
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    });
  };

  const onFocus = () => {
    setIsFocused(true);
  };

  const onBlur = () => {
    setIsFocused(false);
    if (inputValue.trim()) {
      handleAddTag(inputValue);
    }
  };

  const focusInput = () => {
    inputRef.current?.focus();
  };

  return (
    <View className="mb-4">
      {label && (
        <Text className="mb-1 text-sm font-medium text-text-secondary">{label}</Text>
      )}

      <TouchableOpacity
        activeOpacity={0.8}
        onPress={focusInput}
        className={`p-3 rounded-lg border ${
          isFocused ? 'border-primary-500 bg-primary-500/5' : 'border-border bg-primary-500'
        } ${error ? 'border-error' : ''}`}
      >
        <View className="flex flex-row flex-wrap gap-2 mb-2">
          {tags.map(tag => (
            <Animated.View
              key={tag}
              style={{
                transform: [
                  { scale: tagAnimations.current[tag] || new Animated.Value(1) },
                ],
                opacity: tagAnimations.current[tag] || new Animated.Value(1),
              }}
              className="flex-row items-center px-3 py-1.5 bg-accent-500/10 rounded-full"
              accessibilityLabel={`Tag: ${tag}`}
            >
              <Text className="text-sm font-medium text-accent-600">{tag}</Text>
              <TouchableOpacity
                onPress={() => handleRemoveTag(tag)}
                className="ml-2"
                accessibilityLabel={`Remove tag ${tag}`}
                accessibilityRole="button"
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Feather name="x" size={14} color="accent-600" />
              </TouchableOpacity>
            </Animated.View>
          ))}
        </View>

        <View className="flex-row items-center">
          <Feather 
            name="tag" 
            size={16} 
            color={isFocused ? "primary" : "disabled"}
            className="mr-2"
          />
          <TextInput
            ref={inputRef}
            value={inputValue}
            onChangeText={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder={placeholder}
            placeholderTextColor="disabled"
            onFocus={onFocus}
            onBlur={onBlur}
            onSubmitEditing={() => handleAddTag(inputValue)}
            blurOnSubmit={false}
            className="flex-1 text-base text-text-primary"
            accessibilityLabel={accessibilityLabel}
          />
          {inputValue.length > 0 && (
            <TouchableOpacity
              onPress={() => handleAddTag(inputValue)}
              className="ml-2 p-1.5 bg-primary-500 rounded-full"
              accessibilityLabel="Add tag"
              accessibilityRole="button"
            >
              <Feather name="plus" size={14} color="white" />
            </TouchableOpacity>
          )}
        </View>
      </TouchableOpacity>

      {/* Suggestions List */}
      {filteredSuggestions.length > 0 && (
        <View className="mt-2 overflow-hidden border rounded-lg border-border bg-input-background max-h-40">
          {filteredSuggestions.map((suggestion, index) => (
            <TouchableOpacity
              key={`${suggestion}-${index}`}
              onPress={() => handleAddTag(suggestion)}
              className={`px-3 py-2 ${index < filteredSuggestions.length - 1 ? 'border-b border-border' : ''}`}
            >
              <Text className="text-base text-text-primary">{suggestion}</Text>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {error ? (
        <View className="flex-row items-center mt-1">
          <Feather name="alert-circle" size={12} color="error" className="mr-1" />
          <Text className="text-xs text-error">{error}</Text>
        </View>
      ) : (
        tags.length > 0 && (
          <Text className="mt-1 text-xs text-muted-foreground">
            {tags.length} {tags.length === 1 ? 'tag' : 'tags'} added
            {maxTags ? ` (max ${maxTags})` : ''}
          </Text>
        )
      )}
    </View>
  );
};