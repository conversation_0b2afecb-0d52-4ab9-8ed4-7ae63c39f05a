import React from 'react';
import { View, Text } from 'react-native';
import { Control, Controller, FieldErrors } from 'react-hook-form';
import { TagInput } from '@/components/ui/TagInput';
import { interestSuggestions } from '@/constants/suggestions';
import { ProfileFormData } from './ProfileForm'; // Assuming ProfileFormData is exported from ProfileForm

interface InterestsDislikesSectionProps {
  control: Control<ProfileFormData>;
  errors: FieldErrors<ProfileFormData>;
}

const InterestsDislikesSection: React.FC<InterestsDislikesSectionProps> = ({
  control,
  errors,
}) => {
  return (
    <View className="gap-4">
      {/* Interests */}
      <View>
        <Text className="mb-1 font-medium text-muted-foreground">Interests</Text>
        <Controller
          control={control}
          name="interestsInput"
          render={({ field: { onChange, value } }) => (
            <TagInput
              tags={value ? value.split(',').map(i => i.trim()).filter(Boolean) : []}
              onChangeTags={(tags) => onChange(tags.join(', '))}
              placeholder="e.g., hiking, sci-fi movies, baking"
              error={errors.interestsInput?.message}
              accessibilityLabel="Interests input"
              suggestions={interestSuggestions}
            />
          )}
        />
      </View>

      {/* Dislikes */}
      <View>
        <Text className="mb-1 font-medium text-muted-foreground">Dislikes</Text>
        <Controller
          control={control}
          name="dislikesInput"
          render={({ field: { onChange, value } }) => (
            <TagInput
              tags={value ? value.split(',').map(i => i.trim()).filter(Boolean) : []}
              onChangeTags={(tags) => onChange(tags.join(', '))}
              placeholder="e.g., spiders, loud noises, waiting in line"
              error={errors.dislikesInput?.message}
              accessibilityLabel="Dislikes input"
              suggestions={interestSuggestions}
            />
          )}
        />
      </View>
    </View>
  );
};

export default InterestsDislikesSection;