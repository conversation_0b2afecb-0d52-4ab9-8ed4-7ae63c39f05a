import { Redirect } from 'expo-router';

export default function Index() {
  // The root layout (_layout.tsx) handles authentication checks and redirection.
  // This index route can simply redirect to a default screen,
  // and the layout will intercept if authentication is required.
  // Redirecting to '(app)/home' assumes the user is likely authenticated,
  // but the layout will correct this to '(auth)/login' if not.
  return <Redirect href="/(app)/home" />;
}
