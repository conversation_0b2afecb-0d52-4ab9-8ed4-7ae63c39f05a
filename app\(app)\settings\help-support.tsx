import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Linking,
  TextInput,
  Alert,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Stack } from 'expo-router'; // For screen title
import Card from '../../../components/ui/Card';
import Button from '../../../components/ui/Button';
import { Feather } from '@expo/vector-icons';
import * as Application from 'expo-application';
import { useThemeManager } from '@/hooks/useThemeManager'; // Assuming this hook provides theme colors
import tailwindColors from 'tailwindcss/colors'; // For color access

// --- Reusable Components for this Screen ---

interface FaqItemProps {
  question: string;
  answer: string;
}
const FaqItem: React.FC<FaqItemProps> = ({ question, answer }) => (
  <View className="py-3">
    <Text className="text-base font-medium text-text-primary dark:text-text-primary-dark">
      Q: {question}
    </Text>
    <Text className="mt-1 text-sm text-text-secondary dark:text-text-secondary-dark">
      A: {answer}
    </Text>
  </View>
);

interface ActionRowProps {
  label: string;
  onPress: () => void;
  iconName: keyof typeof Feather.glyphMap;
  iconColor: string;
  isLast?: boolean;
}
const ActionRow: React.FC<ActionRowProps> = ({
  label,
  onPress,
  iconName,
  iconColor,
  isLast = false,
}) => (
  <TouchableOpacity
    className={`flex-row items-center justify-between py-3.5 ${
      !isLast ? 'border-b border-border dark:border-border-dark' : ''
    }`}
    onPress={onPress}
    activeOpacity={0.7}
  >
    <Text className="text-base text-text-primary dark:text-text-primary-dark">
      {label}
    </Text>
    <Feather name={iconName} size={22} color={iconColor} />
  </TouchableOpacity>
);

// --- Main Screen Component ---

export default function HelpSupportScreen() {
  const [problemDescription, setProblemDescription] = useState('');
  const { colorScheme } = useThemeManager(); // To determine icon colors based on theme

  // Define icon color - ideally from theme configuration for text-secondary
  const themedIconColor =
    colorScheme === 'dark'
      ? tailwindColors.gray[400] // Corresponds to text-text-secondary-dark
      : tailwindColors.gray[500]; // Corresponds to text-text-secondary-light
  // If your tailwind.config.js directly defines these for 'text-secondary':
  // const themedIconColor = colorScheme === 'dark' ? '#D1D5DB' : '#4B5563';

  // Define placeholder text color - ideally from theme for text-secondary
  const placeholderColor =
    colorScheme === 'dark'
      ? tailwindColors.gray[500] // A bit lighter for placeholder in dark
      : tailwindColors.gray[400]; // A bit darker for placeholder in light

  const faqSections = [
    {
      title: 'Getting Started',
      items: [
        { question: 'How do I add a new profile?', answer: "Navigate to the 'Profiles' tab and tap the '+' icon or the 'Add Profile' button." },
        { question: 'How can I set reminders for birthdays?', answer: 'When adding or editing a profile, you can set specific reminders for their birthday and other custom dates.' },
      ],
    },
    {
      title: 'Managing Profiles',
      items: [
        { question: 'Can I edit profile information after creating it?', answer: "Yes, you can edit any profile details at any time from the profile's page." },
        { question: 'How do I delete a profile?', answer: "On the profile's page, look for the options menu and select 'Delete Profile'." },
      ],
    },
    {
      title: 'Calendar & Reminders',
      items: [
        { question: 'How do I sync the app calendar with my device calendar?', answer: 'Calendar syncing is not available in the current version but is planned for future updates.' },
        { question: 'Can I customize reminder times?', answer: 'Currently, reminder times are fixed, but customization options are under consideration.' },
      ],
    },
    {
      title: 'Account Settings',
      items: [
        { question: 'How do I change my password?', answer: "Password management can be found in the 'Personal Information' section of Settings." },
        { question: 'What happens if I log out?', answer: 'Logging out will require you to enter your credentials the next time you open the app. Your data remains stored securely.' },
      ],
    },
  ];

  const handleContactSupport = () => {
    const email = '<EMAIL>';
    const appVersion = Application.nativeApplicationVersion || 'N/A';
    const osVersion = Platform.Version || 'N/A';
    const subject = `Giftmi App Support Request (v${appVersion})`;
    const body = `App Version: ${appVersion}\nPlatform: ${Platform.OS} ${osVersion}\n\nDescribe your issue below:\n----------------------------------\n`;
    Linking.openURL(`mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`)
      .catch(err => Alert.alert("Error", "Could not open email client. Please contact <NAME_EMAIL>"));
  };

  const handleSubmitReport = () => {
    if (problemDescription.trim() === '') {
        Alert.alert("Input Required", "Please describe the problem before submitting.");
        return;
    }
    const email = '<EMAIL>';
    const subject = 'Giftmi App - Problem Report';
    Linking.openURL(`mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(problemDescription)}`)
      .then(() => {
        Alert.alert("Report Submitted", "Thank you for your feedback. We have received your report and will look into it.");
        setProblemDescription('');
      })
      .catch(err => Alert.alert("Error", "Could not open email client. Please contact <NAME_EMAIL>"));
  };

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-background-dark" edges={['bottom', 'left', 'right']}>
      <Stack.Screen options={{ title: 'Help & Support' }} />
      <ScrollView
        className="flex-1"
        contentContainerClassName="p-4 sm:p-6" // Responsive padding
        showsVerticalScrollIndicator={false}
      >
        {/* FAQs Section */}
        <Card className="p-5 mb-5">
          <Text className="mb-3 text-xl font-semibold text-text-primary dark:text-text-primary-dark">
            Frequently Asked Questions
          </Text>
          {faqSections.map((section, sectionIndex) => (
            <View key={section.title} className={sectionIndex > 0 ? "mt-4" : ""}>
              <Text className="mb-1.5 text-base font-semibold text-text-primary dark:text-text-primary-dark">
                {section.title}
              </Text>
              {section.items.map((faq) => (
                <FaqItem key={faq.question} question={faq.question} answer={faq.answer} />
              ))}
            </View>
          ))}
        </Card>

        {/* Contact Us Section */}
        <Card className="p-5 mb-5">
          <Text className="mb-2 text-xl font-semibold text-text-primary dark:text-text-primary-dark">
            Contact Us
          </Text>
          <ActionRow
            label="Contact Support"
            onPress={handleContactSupport}
            iconName="mail"
            iconColor={themedIconColor}
            isLast={true}
          />
        </Card>

        {/* Report a Problem Section */}
        <Card className="p-5 mb-5">
          <Text className="mb-2 text-xl font-semibold text-text-primary dark:text-text-primary-dark">
            Report a Problem
          </Text>
          <Text className="mb-3 text-sm text-text-secondary dark:text-text-secondary-dark">
            Describe the issue you're experiencing in detail.
          </Text>
          <TextInput
            className="p-3 mt-1 border rounded-lg h-28 border-border dark:border-border-dark text-text-primary dark:text-text-primary-dark bg-input-background dark:bg-input-background-dark"
            placeholder="Please provide as much detail as possible..."
            placeholderTextColor={placeholderColor}
            multiline
            value={problemDescription}
            onChangeText={setProblemDescription}
            textAlignVertical="top" // Ensured for Android
            scrollEnabled={true} // Good for longer multiline inputs
          />
          <Button
            className="mt-4"
            onPress={handleSubmitReport}
            title="Submit Report"
            variant="primary" // Explicitly set variant
          />
        </Card>

        {/* App Information Section */}
        <Card className="p-5">
          <Text className="mb-2 text-xl font-semibold text-text-primary dark:text-text-primary-dark">
            App Information
          </Text>
          <View className="flex-row items-center justify-between py-2">
            <Text className="text-base text-text-primary dark:text-text-primary-dark">App Version</Text>
            <Text className="text-base text-text-secondary dark:text-text-secondary-dark">
              1.0.0
            </Text>
          </View>
          <View className="flex-row items-center justify-between py-2">
            <Text className="text-base text-text-primary dark:text-text-primary-dark">Platform</Text>
            <Text className="text-base text-text-secondary dark:text-text-secondary-dark">
              {Platform.OS} {Platform.Version}
            </Text>
          </View>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
}