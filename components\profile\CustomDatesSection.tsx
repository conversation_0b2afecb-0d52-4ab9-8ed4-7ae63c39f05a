import React, { useState } from 'react';
import { View } from 'react-native';
import { Control, Controller, FieldErrors } from 'react-hook-form';
import { v4 as uuidv4 } from 'uuid'; // Import UUID generator
import { Timestamp } from 'firebase/firestore'; // Import Timestamp
import { CustomDate } from '../../types/firestore'; // Import client-side CustomDate
import CustomDateFormInput from '@/components/profile/CustomDateFormInput';
import Button from '@/components/ui/Button';
import AddCustomDateModal from './AddCustomDateModal';
import { ProfileFormData } from './ProfileForm'; // Assuming ProfileFormData is exported from ProfileForm

// Define the type for CustomDate as stored in react-hook-form state (Date | null)
type CustomDateFormState = {
  id: string;
  name: string;
  date: Date | null;
};

interface CustomDatesSectionProps {
  control: Control<ProfileFormData>;
  errors: FieldErrors<ProfileFormData>;
  profileId: string | null; // Allow null for profileId
}

const CustomDatesSection: React.FC<CustomDatesSectionProps> = ({
  control,
  errors,
  profileId, // Destructure profileId
}) => {
  const [isCustomDateModalVisible, setIsCustomDateModalVisible] = useState(false);

  // Helper function for removing custom dates
  const handleRemoveCustomDate = (
    idToRemove: string,
    onChange: (value: Array<{ id: string; name: string; date: Date | null }>) => void,
    currentItems: Array<{ id: string; name: string; date: Date | null }>
  ) => {
    const updatedItems = currentItems.filter((item) => item.id !== idToRemove);
    onChange(updatedItems); // Update react-hook-form state
  };

  return (
    <View className="gap-3">
      <Controller
        control={control}
        name="customDates"
        render={({ field: { onChange, value } }) => {
          const currentItems = value || [];
          // Handler for adding a new custom date
          const handleAddNewCustomDate = (newItemData: Omit<CustomDateFormState, 'id'>) => {
            const itemWithId: CustomDateFormState = {
              ...newItemData,
              id: uuidv4(), // Generate a unique ID
            };
            onChange([...currentItems, itemWithId]);
          };

          return (
            <>
              {currentItems.map((dateData, index) => {
                // Ensure dateData conforms to the expected type for CustomDateFormInput
                // Convert to CustomDate type for the CustomDateFormInput component
                const customDate: CustomDate = {
                  id: dateData.id,
                  name: dateData.name,
                  date: dateData.date instanceof Date ? Timestamp.fromDate(dateData.date) : null, // Convert Date to Timestamp | null
                };
                return (
                  <CustomDateFormInput
                    key={customDate.id} // Use unique ID as key
                    customDate={customDate}
                    index={index} // Keep index for now, might be useful
                    onRemove={() => handleRemoveCustomDate(customDate.id, onChange, currentItems)}
                  />
                );
              })}
              <Button
                onPress={() => setIsCustomDateModalVisible(true)}
                title="Add Custom Date"
                accessibilityLabel="Add custom date button"
              />

              <AddCustomDateModal
                isVisible={isCustomDateModalVisible}
                onClose={() => setIsCustomDateModalVisible(false)}
                profileId={profileId} // Pass profileId to the modal
                onAddItem={(newItemData: Omit<CustomDateFormState, 'id'>) => {
                  handleAddNewCustomDate(newItemData);
                  setIsCustomDateModalVisible(false);
                }}
              />
            </>
          );
        }}
      />
    </View>
  );
};

export default CustomDatesSection;