# Giftmi Tech Stack

This document outlines the primary technologies used in the Giftmi application.

## Frontend (Mobile App)

*   **Framework:** React Native (managed with Expo)
    *   *Rationale:* Allows for cross-platform development (iOS & Android) from a single codebase, leveraging existing React knowledge. Expo simplifies setup, build processes, and provides access to native APIs.
*   **Language:** TypeScript
    *   *Rationale:* Provides static typing for improved code quality, maintainability, and developer experience.
*   **Styling:** NativeWind (Tailwind CSS for React Native)
    *   *Rationale:* Utility-first CSS framework for rapid UI development and consistent styling. Integrates well with React Native components.
*   **Navigation:** Expo Router
    *   *Rationale:* File-based routing solution for Expo applications, simplifying navigation setup and management.
*   **State Management:**
    *   React Context API (for global state like Authentication)
    *   Zustand / Jotai (Considered for more complex local/feature state management if needed)
    *   *Rationale:* Start simple with Context, adopt more specialized libraries if state complexity increases.

## Backend (Serverless)

*   **Platform:** Firebase
    *   *Rationale:* Provides a comprehensive suite of backend services (BaaS) that integrates well and allows for rapid development.
*   **Authentication:** Firebase Authentication
    *   *Rationale:* Handles secure user login/signup (Email/Password, potentially Google Sign-in).
*   **Database:** Firestore
    *   *Rationale:* NoSQL, scalable, real-time database suitable for storing user profiles, significant other details, and gift ideas. Offers robust security rules.
*   **Serverless Functions:** Firebase Cloud Functions (Node.js with TypeScript)
    *   *Rationale:* Used for backend logic, particularly integrating with the external LLM API, sending notifications, and potentially other background tasks.
*   **Push Notifications:** Firebase Cloud Messaging (FCM)
    *   *Rationale:* Enables sending reminders and other relevant notifications to users.
*   **Storage:** Firebase Storage (Optional)
    *   *Rationale:* Can be used for storing user-generated content like profile pictures if needed in the future.

## AI / Machine Learning

*   **Core Engine:** External Large Language Model (LLM) API
    *   **Provider:** OpenAI (GPT series) or Google (Gemini) - *Initial choice TBD, requires evaluation.*
    *   *Rationale:* Leverages powerful existing models for generating creative and personalized gift recommendations based on structured user input.
*   **Integration:** Via Firebase Cloud Functions
    *   *Rationale:* Functions act as a secure intermediary to handle API calls, prompt construction, and API key management.

## Development & Operations

*   **Version Control:** Git (Hosted on GitHub/GitLab/Bitbucket - *Platform TBD*)
*   **Package Management:** npm
*   **Code Formatting:** Prettier
*   **Linting:** ESLint (with relevant plugins for React Native, TypeScript)
*   **Testing:**
    *   Unit Testing: Jest
    *   Integration Testing: Jest / React Native Testing Library (for function/component interactions)
    *   E2E Testing: Detox / Maestro (*Considered for later stages*)
*   **CI/CD:** *To be defined* (e.g., GitHub Actions, Expo Application Services - EAS Build)
*   **Monitoring:** Firebase Console Monitoring, Google Cloud Logging (*Needs further definition for specific metrics*).
*   **Secret Management:** Firebase Functions Environment Variables / Google Secret Manager.