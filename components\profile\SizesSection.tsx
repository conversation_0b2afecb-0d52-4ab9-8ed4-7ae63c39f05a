import React from 'react';
import { View, Text } from 'react-native';
import { Control, Controller, FieldErrors } from 'react-hook-form';
import Dropdown from '@/components/ui/Dropdown';
import { ProfileFormData } from './ProfileForm'; // Assuming ProfileFormData is exported from ProfileForm

interface SizesSectionProps {
  control: Control<ProfileFormData>;
  errors: FieldErrors<ProfileFormData>;
}

const clothingSizeOptions = [
  { label: 'Select Clothing Size', value: '' },
  { label: 'XS', value: 'XS' },
  { label: 'S', value: 'S' },
  { label: 'M', value: 'M' },
  { label: 'L', value: 'L' },
  { label: 'XL', value: 'XL' },
  { label: 'XXL', value: 'XXL' },
  { label: 'XXXL', value: 'XXXL' },
  { label: 'Other', value: 'Other' },
];

const shoeSizeOptions = [
  { label: 'Select Shoe Size (US)', value: '' },
  { label: '6', value: '6' }, { label: '6.5', value: '6.5' },
  { label: '7', value: '7' }, { label: '7.5', value: '7.5' },
  { label: '8', value: '8' }, { label: '8.5', value: '8.5' },
  { label: '9', value: '9' }, { label: '9.5', value: '9.5' },
  { label: '10', value: '10' }, { label: '10.5', value: '10.5' },
  { label: '11', value: '11' }, { label: '11.5', value: '11.5' },
  { label: '12', value: '12' }, { label: '13', value: '13' },
  { label: '14', value: '14' },
  { label: 'Other', value: 'Other' },
];

const SizesSection: React.FC<SizesSectionProps> = ({
  control,
  errors,
}) => {
  return (
    <View className="gap-3">
      {/* Clothing Size */}
      <View>
        <Text className="mb-1 text-sm font-medium text-muted-foreground">Clothing Size</Text>
        <Controller
          control={control}
          name="clothingSize"
          render={({ field: { onChange, onBlur, value } }) => (
            <Dropdown
              options={clothingSizeOptions}
              selectedValue={value}
              onValueChange={onChange}
              placeholder="Select Clothing Size"
              error={errors.clothingSize?.message}
              accessibilityLabel="Select clothing size dropdown"
            />
          )}
        />
      </View>
      {/* Shoe Size */}
      <View>
        <Text className="mb-1 text-sm font-medium text-muted-foreground">Shoe Size</Text>
        <Controller
          control={control}
          name="shoeSize"
          render={({ field: { onChange, onBlur, value } }) => (
            <Dropdown
              options={shoeSizeOptions}
              selectedValue={value}
              onValueChange={onChange}
              placeholder="Select Shoe Size"
              error={errors.shoeSize?.message}
              accessibilityLabel="Select shoe size dropdown"
            />
          )}
        />
      </View>
    </View>
  );
};

export default SizesSection;