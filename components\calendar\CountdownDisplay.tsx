import React from 'react';
import { View, Text } from 'react-native';
import { format } from 'date-fns';
import { Feather } from '@expo/vector-icons';
import { CalendarEvent } from '@/hooks/useCalendarData'; // Import CalendarEvent type
import Animated, { FadeIn, FadeOut, SlideInUp } from 'react-native-reanimated';
import { getEventTypeInfo, EventTypeInfo } from './CalendarEventList'; // Import from CalendarEventList

interface CountdownDisplayProps {
  closestDate: CalendarEvent | null;
}

const CountdownDisplay: React.FC<CountdownDisplayProps> = ({ closestDate }) => {
  if (!closestDate) {
    return (
      <Animated.View 
        entering={FadeIn.duration(300)} 
        exiting={FadeOut.duration(200)} 
        className="items-center py-6 mb-4 border shadow-sm bg-card dark:bg-card-dark rounded-xl border-border/30 dark:border-border-dark"
      >
        <Feather name="calendar" size={24} color={'#E87900'} />
        <Text className="mt-2 text-base text-center text-text-secondary dark:text-text-secondary-dark">
          No upcoming dates found
        </Text>
      </Animated.View>
    );
  }

  const { name, type, date, daysUntil } = closestDate;

  const { icon, color } = getEventTypeInfo(type);

  // Determine urgency class based on days until
  const getUrgencyClass = () => {
    if (daysUntil <= 3) return 'bg-error/10';
    if (daysUntil <= 7) return 'bg-primary-500/10';
    if (daysUntil <= 14) return 'bg-accent-500/10';
    return 'bg-background';
  };

  return (
    <Animated.View 
      entering={SlideInUp.duration(400)} 
      exiting={FadeOut.duration(300)} 
      className="p-4 border shadow-sm bg-card dark:bg-card-dark rounded-xl border-border/30 dark:border-border-dark"
    >
      <Text className="text-lg font-semibold text-text-primary dark:text-text-primary-dark">Closest Upcoming Date</Text>
      <View className="mt-3">
        <View className="flex-row items-center mb-2">
          <View className="p-2 rounded-full" style={{ backgroundColor: `${"#A3002B"}20` }}>
            <Feather name={icon as any} size={20} color={"#A3002B"} />
          </View>
          <Text className="ml-4 text-base font-semibold text-text-primary dark:text-text-primary-dark">{name}</Text>
        </View>
        <Text className="text-sm text-text-secondary dark:text-text-secondary-dark">
          {type} on {format(date, 'MMMM d, yyyy')}
        </Text>
        <View className="flex-row items-center mt-3">
          <View className={`flex-row mt-2 items-center px-4 py-2 rounded-full ${getUrgencyClass()}`}>
            <Text className="text-lg font-bold text-primary-500">{daysUntil}</Text>
            <Text className="ml-4 font-medium text-text-secondary dark:text-text-secondary-dark">
              {daysUntil === 1 ? 'day' : 'days'} away
            </Text>
          </View>
        </View>
      </View>
    </Animated.View>
  );
};

export default CountdownDisplay;