{"name": "giftmi", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "emulators": "firebase emulators:start --import=./firebase-emulator-data --export-on-exit", "emulators:clean": "firebase emulators:start --import=./firebase-emulator-data --export-on-exit --only firestore,auth,functions"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@hookform/resolvers": "^5.0.1", "@react-native-async-storage/async-storage": "^1.24.0", "@react-native-community/datetimepicker": "8.2.0", "@react-native-picker/picker": "^2.11.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@types/date-fns": "^2.6.3", "date-fns": "^4.1.0", "expo": "~52.0.43", "expo-auth-session": "~6.0.3", "expo-blur": "~14.0.3", "expo-constants": "~17.0.8", "expo-crypto": "~14.0.2", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-linking": "~7.0.5", "expo-router": "~4.0.20", "expo-splash-screen": "~0.29.22", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.9", "expo-web-browser": "~14.0.2", "firebase": "^11.6.0", "nativewind": "^4.1.23", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.55.0", "react-native": "0.76.9", "react-native-calendars": "^1.1311.0", "react-native-gesture-handler": "~2.20.2", "react-native-get-random-values": "^1.11.0", "react-native-reanimated": "^3.16.2", "react-native-safe-area-context": "^4.12.0", "react-native-screens": "~4.4.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "tailwindcss": "^3.4.17", "uuid": "^11.1.0", "zod": "^3.24.2", "expo-localization": "~16.0.1", "expo-notifications": "~0.29.14", "expo-device": "~7.0.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "jest": "^29.2.1", "jest-expo": "~52.0.6", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true}