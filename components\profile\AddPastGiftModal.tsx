import React, { useState } from 'react';
import { Timestamp } from 'firebase/firestore';
import { View, Text, Modal, TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import Input from '@/components/ui/Input';
import Button from '@/components/ui/Button';
import { PastGiftGiven } from '@/functions/src/types/firestore';
const formatDate = (date: Date | null | undefined | Timestamp): string => {
  if (!date) return 'Select Date';
  let dateObject: Date;

  if (date instanceof Timestamp) {
    dateObject = date.toDate();
  } else if (date instanceof Date) {
    dateObject = date;
  } else {
    return 'Select Date';
  }

  return dateObject.toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

interface AddPastGiftModalProps {
  isVisible: boolean;
  onClose: () => void;
  onAddItem: (
    newItem: Omit<PastGiftGiven, 'date'> & { date: Date | null }
  ) => void;
}

const AddPastGiftModal: React.FC<AddPastGiftModalProps> = ({
  isVisible,
  onClose,
  onAddItem,
}) => {
  const [item, setItem] = useState('');
  const [occasion, setOccasion] = useState('');
  const [date, setDate] = useState<Date | null>(null);
  const [reaction, setReaction] = useState('');
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [error, setError] = useState('');

  const handleGiftDateConfirm = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setDate(selectedDate);
    }
  };

  const handleSave = () => {
    if (!item.trim()) {
      setError('Item name is required');
      return;
    }

    onAddItem({
      item: item.trim(),
      occasion: occasion.trim(),
      date,
      reaction: reaction.trim(),
    });

    // Reset form
    setItem('');
    setOccasion('');
    setDate(null);
    setReaction('');
    setError('');
    onClose();
  };

  const handleCancel = () => {
    setItem('');
    setOccasion('');
    setDate(null);
    setReaction('');
    setError('');
    onClose();
  };

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      transparent
      onRequestClose={handleCancel}
    >
      <View
        style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
        className="items-center justify-center flex-1 bg-black/50"
      >
        <View className="w-full max-w-sm p-6 border rounded-lg border-border bg-card">
          <Text className="mb-4 text-xl font-bold text-foreground">
            Add Past Gift
          </Text>

          <View className="gap-4">
            <View>
              <Text className="mb-1 font-medium text-muted-foreground">
                Item Name *
              </Text>
              <Input
                placeholder="e.g., Concert tickets"
                onChangeText={setItem}
                value={item}
                error={error}
                accessibilityLabel="Item name input"
              />
            </View>

            <View>
              <Text className="mb-1 font-medium text-muted-foreground">
                Occasion
              </Text>
              <Input
                placeholder="e.g., Birthday 2023"
                onChangeText={setOccasion}
                value={occasion}
                accessibilityLabel="Occasion input"
              />
            </View>

            <View>
              <Text className="mb-1 font-medium text-muted-foreground">
                Date Given
              </Text>
              <TouchableOpacity
                onPress={() => setShowDatePicker(true)}
                className="flex-row items-center justify-between p-3 border rounded-md border-border bg-input-background"
                accessibilityLabel="Select date given"
                accessibilityRole="button"
              >
                <Text
                  className={
                    !date ? 'text-muted-foreground' : 'text-foreground'
                  }
                >
                  {formatDate(date)}
                </Text>
                <Feather name="calendar" size={20} color="#6b7280" />
              </TouchableOpacity>
            </View>

            {showDatePicker ? (
              <DateTimePicker
                value={date || new Date()}
                mode="date"
                display="default"
                onChange={handleGiftDateConfirm}
              />
            ) : null}

            <View>
              <Text className="mb-1 font-medium text-muted-foreground">
                Reaction
              </Text>
              <Input
                placeholder="e.g., Loved it, uses it all the time!"
                onChangeText={setReaction}
                value={reaction}
                accessibilityLabel="Reaction input"
              />
            </View>

            <View className="flex-row justify-end w-full gap-2 mt-4">
              <Button
                onPress={handleCancel}
                title="Cancel"
                accessibilityLabel="Cancel button"
                className="w-1/2"
              />
              <Button
                onPress={handleSave}
                title="Save Gift"
                accessibilityLabel="Save gift button"
                className="w-1/2"
              />
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default AddPastGiftModal;
