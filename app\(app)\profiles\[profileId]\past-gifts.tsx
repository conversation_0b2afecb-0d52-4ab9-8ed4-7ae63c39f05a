import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import Animated, { FadeIn, SlideInRight } from 'react-native-reanimated';
import { useColorScheme } from 'nativewind';
import { format } from 'date-fns';

import { useAuth } from '../../../../contexts/AuthContext';
import {
  getSignificantOtherById,
  updateSignificantOther
} from '../../../../services/profileService';
import { SignificantOtherProfile, PastGiftGiven } from '../../../../functions/src/types/firestore';
import LoadingIndicator from '../../../../components/ui/LoadingIndicator';
import Card from '../../../../components/ui/Card';
import Button from '../../../../components/ui/Button';

const PastGiftsScreen = () => {
  const { profileId } = useLocalSearchParams();
  const router = useRouter();
  const { user } = useAuth();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [profile, setProfile] = useState<SignificantOtherProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deletingIds, setDeletingIds] = useState<Set<string>>(new Set());

  const id = Array.isArray(profileId) ? profileId[0] : profileId;

  const fetchData = useCallback(async () => {
    if (!user?.uid || !id) return;

    try {
      setLoading(true);
      setError(null);

      const profileData = await getSignificantOtherById(user.uid, id);
      setProfile(profileData);
    } catch (err) {
      setError('Failed to load past gifts. Please try again.');
      console.error('Error fetching past gifts:', err);
    } finally {
      setLoading(false);
    }
  }, [user?.uid, id]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleDeleteGift = async (giftIndex: number) => {
    if (!profile || !user?.uid) return;

    const gift = profile.pastGiftsGiven?.[giftIndex];
    if (!gift) return;

    Alert.alert(
      'Delete Gift',
      `Are you sure you want to delete "${gift.item}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const giftId = `gift-${giftIndex}`;
            setDeletingIds(prev => new Set(prev).add(giftId));

            try {
              const updatedGifts = profile.pastGiftsGiven?.filter((_, index) => index !== giftIndex) || [];
              await updateSignificantOther(user.uid, id, {
                pastGiftsGiven: updatedGifts,
              });

              setProfile(prev => prev ? { ...prev, pastGiftsGiven: updatedGifts } : null);
            } catch (err) {
              Alert.alert('Error', 'Failed to delete gift. Please try again.');
            } finally {
              setDeletingIds(prev => {
                const newSet = new Set(prev);
                newSet.delete(giftId);
                return newSet;
              });
            }
          },
        },
      ]
    );
  };

  const renderGiftItem = ({ item, index }: { item: PastGiftGiven; index: number }) => {
    const giftId = `gift-${index}`;
    const isDeleting = deletingIds.has(giftId);

    return (
      <Animated.View
        entering={SlideInRight.delay(index * 100).duration(400)}
        className="mb-4"
      >
        <Card className="p-4">
          <View className="flex-row items-start justify-between">
            <View className="flex-1 mr-3">
              <View className="flex-row items-center mb-2">
                <View className="p-2 rounded-full mr-3" style={{ backgroundColor: '#7C3AED20' }}>
                  <Feather name="gift" size={18} color="#7C3AED" />
                </View>
                <View className="flex-1">
                  <Text className="text-base font-semibold text-text-primary dark:text-text-primary-dark">
                    {item.item}
                  </Text>
                  {item.occasion && (
                    <Text className="text-sm text-text-secondary dark:text-text-secondary-dark">
                      For {item.occasion}
                    </Text>
                  )}
                </View>
              </View>

              {item.reaction && (
                <Text className="text-sm text-text-secondary dark:text-text-secondary-dark mb-2 ml-12">
                  {item.reaction}
                </Text>
              )}

              <View className="flex-row items-center ml-12">
                {item.date && (
                  <Text className="text-xs text-text-secondary dark:text-text-secondary-dark mr-4">
                    Given: {format(item.date.toDate(), 'MMM d, yyyy')}
                  </Text>
                )}
              </View>
            </View>

            <TouchableOpacity
              onPress={() => handleDeleteGift(index)}
              disabled={isDeleting}
              className="p-2 rounded-full active:bg-error/10"
            >
              {isDeleting ? (
                <LoadingIndicator size="small" color="#DC2626" />
              ) : (
                <Feather name="trash-2" size={18} color="#DC2626" />
              )}
            </TouchableOpacity>
          </View>
        </Card>
      </Animated.View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <View className="flex-1 items-center justify-center">
          <LoadingIndicator size="large" color="#A3002B" />
          <Text className="mt-4 text-base text-text-secondary dark:text-text-secondary-dark">
            Loading past gifts...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <View className="flex-1 items-center justify-center px-6">
          <Feather name="alert-circle" size={48} color="#DC2626" />
          <Text className="mt-4 text-lg font-semibold text-center text-error dark:text-error-dark">
            {error}
          </Text>
          <Button
            title="Try Again"
            onPress={fetchData}
            variant="primary"
            className="mt-4"
          />
        </View>
      </SafeAreaView>
    );
  }

  const gifts = profile?.pastGiftsGiven || [];

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
      <View className="flex-1 px-4 py-6">
        {profile && (
          <View className="mb-6">
            <Text className="text-2xl font-bold text-text-primary dark:text-text-primary-dark">
              {profile.name}'s Past Gifts
            </Text>
            <Text className="text-base text-text-secondary dark:text-text-secondary-dark">
              Gifts you've given in the past
            </Text>
          </View>
        )}

        {gifts.length === 0 ? (
          <Animated.View
            entering={FadeIn.duration(600)}
            className="flex-1 items-center justify-center px-6"
          >
            <View className="items-center justify-center w-20 h-20 mb-6 rounded-full bg-primary/10 dark:bg-primary-dark/10">
              <Feather
                name="gift"
                size={32}
                color={isDark ? '#C70039' : '#A3002B'}
              />
            </View>
            <Text className="text-lg font-semibold text-center text-text-primary dark:text-text-primary-dark mb-2">
              No Past Gifts Yet
            </Text>
            <Text className="text-base text-center text-text-secondary dark:text-text-secondary-dark mb-6">
              Keep track of gifts you've given to {profile?.name}
            </Text>
            <Button
              title="Add First Gift"
              onPress={() => router.push(`/profiles/${id}/edit?focusSection=pastGiftsGiven`)}
              variant="primary"
            />
          </Animated.View>
        ) : (
          <FlatList
            data={gifts}
            renderItem={renderGiftItem}
            keyExtractor={(_, index) => `gift-${index}`}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 20 }}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

export default PastGiftsScreen;
