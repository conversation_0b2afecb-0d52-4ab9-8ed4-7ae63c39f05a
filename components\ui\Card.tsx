import React from 'react';
import { View, ViewProps, ViewStyle } from 'react-native';

interface CardProps extends ViewProps {
  children: React.ReactNode;
  style?: ViewStyle; // Allow style overrides
}

const Card = React.forwardRef<View, CardProps>(({ children, style, ...props }, ref) => {
  // Consistent padding, rounded corners, background, and a subtle border
  // Adjust classes based on memorybank/style_guide.md and tailwind.config.js if needed
  const cardClasses = 'bg-card p-4 rounded-lg border border-border shadow-md';

  return (
    <View ref={ref} className={cardClasses} style={style} {...props}>
      {children}
    </View>
  );
});

export default Card;