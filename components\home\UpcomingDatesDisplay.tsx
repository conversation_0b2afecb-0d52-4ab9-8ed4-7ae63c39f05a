import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons';
import Animated, { FadeIn, SlideInUp } from 'react-native-reanimated';
import { useColorScheme } from 'nativewind';
import { format } from 'date-fns';

import { CalendarEvent } from '../../hooks/useCalendarData';

interface UpcomingDatesDisplayProps {
  upcomingDates: CalendarEvent[];
  onAddDatePress?: () => void;
  className?: string;
}

// Helper function to get event type styling
const getEventTypeInfo = (type: string) => {
  switch (type.toLowerCase()) {
    case 'birthday':
      return { icon: 'gift', color: '#A3002B', bgColor: '#A3002B20' };
    case 'anniversary':
      return { icon: 'heart', color: '#E87900', bgColor: '#E8790020' };
    case 'holiday':
      return { icon: 'star', color: '#16A34A', bgColor: '#16A34A20' };
    case 'custom':
    default:
      return { icon: 'calendar', color: '#6366F1', bgColor: '#6366F120' };
  }
};

// Helper function to get urgency styling
const getUrgencyStyle = (daysUntil: number) => {
  if (daysUntil <= 3) return 'bg-error/10 border-error/20';
  if (daysUntil <= 7) return 'bg-primary/10 border-primary/20';
  if (daysUntil <= 14) return 'bg-accent/10 border-accent/20';
  return 'bg-card dark:bg-card-dark border-border dark:border-border-dark';
};

const UpcomingDatesDisplay: React.FC<UpcomingDatesDisplayProps> = ({
  upcomingDates,
  onAddDatePress,
  className = ''
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Show empty state if no upcoming dates
  if (!upcomingDates || upcomingDates.length === 0) {
    return (
      <Animated.View
        entering={FadeIn.duration(600)}
        className={`mb-6 ${className}`}
      >
        <View className="p-6 border rounded-xl bg-card dark:bg-card-dark border-border dark:border-border-dark">
          <View className="items-center">
            <View className="items-center justify-center w-16 h-16 mb-4 rounded-full bg-primary/10 dark:bg-primary-dark/10">
              <Feather
                name="calendar-plus"
                size={24}
                color={isDark ? '#C70039' : '#A3002B'}
              />
            </View>
            <Text className="mb-2 text-lg font-semibold text-center text-text-primary dark:text-text-primary-dark">
              No major dates soon?
            </Text>
            <Text className="mb-4 text-center text-text-secondary dark:text-text-secondary-dark">
              Maybe plan a surprise!
            </Text>
            {onAddDatePress && (
              <TouchableOpacity
                onPress={onAddDatePress}
                className="px-4 py-2 rounded-lg bg-primary dark:bg-primary-dark"
              >
                <Text className="font-medium text-white">
                  Add a Special Date
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </Animated.View>
    );
  }

  // Show upcoming dates (limit to 3)
  const displayDates = upcomingDates.slice(0, 3);

  return (
    <Animated.View
      entering={FadeIn.duration(600)}
      className={`mb-6 ${className}`}
    >
      <View className="flex-row items-center justify-between mb-3">
        <Text className="text-lg font-semibold text-text-primary dark:text-text-primary-dark">
          Upcoming Dates
        </Text>
        {onAddDatePress && (
          <TouchableOpacity
            onPress={onAddDatePress}
            className="p-2 rounded-full active:bg-gray-100 dark:active:bg-gray-800"
          >
            <Feather
              name="plus"
              size={20}
              color={isDark ? '#C70039' : '#A3002B'}
            />
          </TouchableOpacity>
        )}
      </View>

      <View className="space-y-3">
        {displayDates.map((dateEvent, index) => {
          const { icon, color, bgColor } = getEventTypeInfo(dateEvent.type);
          const urgencyStyle = getUrgencyStyle(dateEvent.daysUntil);

          return (
            <Animated.View
              key={`${dateEvent.name}-${dateEvent.date.getTime()}`}
              entering={SlideInUp.delay(index * 100).duration(400)}
              className={`p-4 border rounded-xl shadow-sm ${urgencyStyle}`}
            >
              <View className="flex-row items-center">
                <View
                  className="p-2 mr-3 rounded-full"
                  style={{ backgroundColor: bgColor }}
                >
                  <Feather
                    name={icon as any}
                    size={18}
                    color={color}
                  />
                </View>

                <View className="flex-1">
                  <Text className="text-base font-semibold text-text-primary dark:text-text-primary-dark">
                    {dateEvent.name}
                  </Text>
                  <Text className="text-sm text-text-secondary dark:text-text-secondary-dark">
                    {format(dateEvent.date, 'MMMM d, yyyy')}
                  </Text>
                </View>

                <View className="items-end">
                  <View className="px-3 py-1 rounded-full bg-primary/10 dark:bg-primary-dark/10">
                    <Text className="text-sm font-bold text-primary dark:text-primary-dark">
                      {dateEvent.daysUntil}
                    </Text>
                  </View>
                  <Text className="mt-1 text-xs text-text-secondary dark:text-text-secondary-dark">
                    {dateEvent.daysUntil === 1 ? 'day' : 'days'}
                  </Text>
                </View>
              </View>
            </Animated.View>
          );
        })}
      </View>
    </Animated.View>
  );
};

export default UpcomingDatesDisplay;
