# Giftmi Style Guide

This document outlines the visual and interaction design principles for the Giftmi application, aiming for a clean, minimalistic, and inviting user experience enhanced by smooth animations.

## Guiding Principles

*   **Minimalism:** Keep the UI clean and uncluttered. Focus on essential elements and clear visual hierarchy. Avoid excessive decoration.
*   **Clarity:** Ensure information is presented clearly and actions are intuitive.
*   **Inviting:** Use subtle animations, thoughtful micro-interactions, and a calming color palette to create a welcoming and engaging experience.
*   **Consistency:** Apply styles, spacing, and interaction patterns uniformly throughout the app.

## Color Palette

*   **Primary (Premium Orange):**
    *   `primary-500`: '#E87900' (Deep, rich orange for main actions)
    *   `primary-600`: '#CC6A00' (Darker orange for hover/pressed states)
*   **Secondary/Accent (Sophisticated Teal):**
    *   `accent-500`: '#0D9488' (Deep teal for secondary actions, highlights)
    *   `accent-600`: '#0F766E' (Darker teal)
*   **Neutrals:**
    *   `background`: '#F9FAFB' (Very light gray - Off-white)
    *   `card`: '#FFFFFF' (White - for card backgrounds)
    *   `text-primary`: '#1F2937' (Dark Gray - for main text)
    *   `text-secondary`: '#4B5563' (Slightly darker Medium Gray - for secondary text)
    *   `border`: '#D1D5DB' (Slightly darker Light Gray - for borders, dividers)
    *   `input-background`: '#FFFFFF'
    *   `disabled`: '#9CA3AF' (Medium Gray for disabled states)
*   **Feedback:**
    *   `error`: '#EF4444' (Red)
    *   `success`: '#22C55E' (Green)
*   **Usage:** Use color purposefully. Define these roles (e.g., `primary`, `accent`, `text-primary`) in `tailwind.config.js` under `theme.extend.colors`. Ensure sufficient contrast.

## Typography

*   **Font Family:** Choose 1-2 clean, readable sans-serif fonts available via `expo-font`. *(TBD - Requires font selection and loading)*
*   **Hierarchy:** Define clear typographic scale (e.g., H1, H2, Body Large, Body Small, Caption) using font sizes and weights. Use `tailwind.config.js` to configure font sizes.
*   **Readability:** Ensure adequate line height and letter spacing for comfortable reading. Use neutral text colors with good contrast against backgrounds.

## Component Library Usage (NativeWind)

*   **Base Components:** Utilize core React Native components styled with NativeWind utilities.
*   **Custom Components:** Create reusable custom components (`components/ui/`) for common patterns (Buttons, Inputs, Cards, Modals) encapsulating NativeWind styles.
*   **Consistency:** Apply consistent padding, margins, border radius, and shadow utilities defined in `tailwind.config.js`.

## Layout & Spacing

*   **Grid/Alignment:** Use consistent alignment principles. While not a strict grid system, maintain visual alignment of elements.
*   **Spacing:** Define a spacing scale (e.g., 4px or 8px base unit) in `tailwind.config.js` and use these consistently for padding, margins, and gaps (`p-4`, `m-2`, `gap-8`, etc.). Avoid arbitrary spacing values.
*   **Whitespace:** Embrace whitespace to improve readability and reduce clutter, reinforcing the minimalistic aesthetic.

## Iconography

*   **Library:** Use a consistent icon set, preferably vector-based. `@expo/vector-icons` provides several options (e.g., Feather, MaterialCommunityIcons). Choose one set primarily. `expo-symbols` (SF Symbols for iOS) can also be used where appropriate.
*   **Style:** Icons should match the minimalistic style (e.g., line icons vs. filled icons).
*   **Usage:** Use icons purposefully to enhance clarity and visual appeal, not just for decoration. Ensure icons have appropriate `accessibilityLabel`s when interactive.

## Animations & Micro-interactions

*   **Library:** Utilize `react-native-reanimated` (v3.16.2) for performant animations.
*   **Purpose:** Animations should be subtle, smooth, and meaningful. They should enhance the user experience, provide feedback, guide attention, or smooth transitions, not distract or slow down the user.
*   **Examples:**
    *   **Transitions:** Smooth screen transitions (fade, slide).
    *   **Button Presses:** Subtle scale or opacity change on press. Haptic feedback (`expo-haptics`) can complement visual feedback.
    *   **Loading States:** Use subtle loading indicators (e.g., spinners, skeleton loaders with shimmer animations).
    *   **State Changes:** Animate changes in component state where appropriate (e.g., expanding/collapsing sections, highlighting selected items).
*   **Performance:** Ensure animations run smoothly on the UI thread. Avoid computationally expensive animations. Test on target devices.

## Tone of Voice

*   **App Copy:** Friendly, supportive, encouraging, and clear. Avoid jargon. Focus on helping the user achieve their goal of finding a great gift.

## Accessibility (a11y) Guidelines

*   **Contrast:** Ensure sufficient color contrast between text and background (WCAG AA minimum).
*   **Labels:** Provide clear `accessibilityLabel`s for interactive elements (buttons, inputs, icons).
*   **Dynamic Type:** Support dynamic type scaling where possible.
*   **Testing:** Test with screen readers (VoiceOver, TalkBack) and other accessibility tools.

*(Specific color values, font choices, and detailed component examples to be added as design progresses.)*