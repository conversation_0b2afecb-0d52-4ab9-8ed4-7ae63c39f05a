# Enhanced Calendar/Dates Screen: Brainstorming & Plan

This plan addresses the user ideas and technical considerations outlined in the initial request.

**1. Refined Feature Set:**

*   **Core:**
    *   **Visual Calendar:** Monthly grid view displaying important dates (`react-native-calendars` seems suitable).
    *   **Date Sources:** Display birthdays and anniversaries from existing `significant_others` profiles.
    *   **Custom Dates:** Allow users to add, edit, and delete profile-specific custom dates (e.g., "First Date," "Pet's Birthday") with a name and date.
    *   **Countdown:** Display a prominent countdown to the *nearest* upcoming important date (from any profile, including birthdays, anniversaries, and custom dates).
*   **Relationship-Aware Features:**
    *   **Standard Dates:** Automatically calculate and display relevant standard dates (e.g., Valentine's Day, Mother's Day, Father's Day) based on the `relationship` field in the `significant_others` profile.
    *   **Improved Relationship Options:** Update the available `relationship` choices to better facilitate the mapping of standard dates.
*   **UI/UX Enhancements:**
    *   **Visual Distinction:** Use distinct visual markers (e.g., icons, color-coded dots) on the calendar grid to differentiate between date types (Birthday, Anniversary, Custom, Standard Holiday).
    *   **Multi-Event Handling:** Implement a clear way to indicate multiple events on a single day (e.g., multiple dots, a list view upon tapping the day).
    *   **Integrated Date Management:** Allow adding/editing custom dates both via the `ProfileForm` and potentially directly by interacting with the calendar screen (e.g., tapping a day).

**2. Data Model Proposals (Firestore):**

*   **`significant_others` Collection Updates:**
    *   Add a new field to store custom dates:
        ```typescript
        // types/firestore.ts (or a new types/dates.ts)
        interface CustomDate {
          id: string; // Unique ID for the custom date (e.g., generated UUID)
          name: string; // e.g., "First Date Anniversary", "Fluffy's Birthday"
          date: Timestamp; // Firestore Timestamp for the specific date
          // Consider adding recurrence in a future phase if needed
          // recurring?: 'yearly' | 'monthly' | null;
        }

        // Existing SignificantOther interface update
        interface SignificantOther {
          // ... existing fields (userId, profileId, name, relationship, birthday, anniversary, etc.)
          customDates?: CustomDate[]; // Array to hold custom dates
        }
        ```
    *   **Rationale:** Embedding custom dates directly within the profile document keeps related data together and simplifies fetching profile-specific information. Firestore's document size limits should be considered, but are unlikely to be an issue for a reasonable number of custom dates per profile.

*   **Standard Dates Management:**
    *   **Proposal:** Use a **Cloud Function** combined with a **Date Calculation Library** (like `date-fns` or potentially a more specialized holiday calculation library if available for Node.js).
    *   **Rationale:**
        *   Handles complex recurring dates (e.g., "second Sunday in May") accurately.
        *   Keeps client-side logic simpler.
        *   Can be updated centrally if calculation logic changes.
        *   Avoids storing potentially large, static lists of dates in Firestore.
    *   **Implementation Sketch:**
        1.  Client requests dates for a specific month/year range.
        2.  Client fetches relevant `significant_others` profiles (including `relationship` field).
        3.  Client calls a Cloud Function (`getStandardDates`) passing the year and the list of unique relationships present in the fetched profiles.
        4.  The Cloud Function uses the date library and relationship mapping logic to calculate the relevant standard dates for that year and those relationships.
        5.  Function returns a list of standard dates `{ name: string, date: Timestamp, relationshipAssociation: string }`.
    *   **Recurrence:** Handled by the date calculation logic within the Cloud Function/library.
    *   **Regional Variations:** This is complex.
        *   *MVP Approach:* Focus on a primary region (e.g., US-based standard holidays) initially. State this limitation clearly.
        *   *Future Enhancement:* Could potentially pass device locale to the Cloud Function, or add a user setting for preferred holiday region, requiring more complex logic in the function.

**3. UI/UX Considerations:**

*   **Calendar Library:** `react-native-calendars` offers features like marking dates with dots, custom markers, and handling date selection, making it a strong candidate.
*   **Visual Markers:**
    *   Use `markingType={'multi-dot'}` or custom markers.
    *   Define a clear color/icon scheme: e.g., Blue dot = Birthday, Pink dot = Anniversary, Green dot = Custom Date, Orange dot = Standard Holiday. Provide a legend if necessary.
*   **Multiple Events:** `react-native-calendars` supports multiple dots. Tapping a day could open a modal or bottom sheet listing all events for that day.
*   **Countdown Display:** Place prominently, perhaps in the screen header or a dedicated card above the calendar grid. Logic needs to find the minimum future date across all profiles and date types.
*   **Custom Date Management:**
    *   **Profile Form:** Add a new section similar to "Interests" or "Past Gifts" using a list management pattern (Add button -> Modal form for Name/Date -> List display -> Edit/Delete options). This aligns with existing patterns.
    *   **Calendar Interaction (Optional - Phase 2/3):** Tapping an empty day could present an "Add Custom Date" option. Tapping a day with events could show the event list with an "Add" button. This adds complexity but improves usability.

**4. Updated `relationshipOptions`:**

Propose expanding the list to better map standard dates. This list should be stored centrally (e.g., `constants/relationships.ts`) and used in the `ProfileForm`.

```typescript
// constants/relationships.ts (Example)
export const relationshipOptions = [
  { label: 'Partner', value: 'partner' }, // Valentine's, Anniversary
  { label: 'Spouse', value: 'spouse' }, // Valentine's, Anniversary
  { label: 'Mother', value: 'mother' }, // Mother's Day
  { label: 'Father', value: 'father' }, // Father's Day
  { label: 'Parent', value: 'parent' }, // Mother's/Father's Day (or generic Parent's Day if applicable)
  { label: 'Sibling', value: 'sibling' }, // Siblings Day? (Less common standard)
  { label: 'Child', value: 'child' },
  { label: 'Friend', value: 'friend' }, // Friendship Day?
  { label: 'Grandparent', value: 'grandparent' }, // Grandparents' Day
  { label: 'Other', value: 'other' }, // No standard date mapping
];

// Need mapping logic (likely in the Cloud Function)
// e.g., if relationship is 'mother' or 'parent', calculate Mother's Day.
```

**5. Technical Implementation Notes:**

*   **Data Fetching:**
    1.  Fetch all `significant_others` documents for the current user from Firestore.
    2.  Extract birthdays, anniversaries, and custom dates.
    3.  Identify unique relationships from the profiles.
    4.  Call the `getStandardDates` Cloud Function with the year and unique relationships.
    5.  Combine all date sources (profile dates, custom dates, standard dates) into a single structure suitable for `react-native-calendars` marked dates prop.
    6.  Calculate the nearest upcoming date for the countdown component from the combined list.
*   **Libraries:** `react-native-calendars`, `date-fns` (or similar for date calculations/formatting, potentially in the Cloud Function).
*   **State Management:** Use component state (`useState`) for the calendar display month. Fetched/combined dates could be managed in component state or a dedicated state management solution (like Zustand/Jotai) if the screen becomes very complex or shares state heavily.

**6. Phased Implementation Approach:**

*   **Phase 1 (MVP):**
    *   Implement the visual calendar grid (`react-native-calendars`).
    *   Display existing birthdays and anniversaries from profiles.
    *   Implement the countdown logic for birthdays/anniversaries only.
    *   Implement adding/editing/deleting **custom dates** via the `ProfileForm`.
    *   Display custom dates on the calendar.
    *   Update countdown logic to include custom dates.
    *   Use basic visual markers (e.g., single dot).
*   **Phase 2 (Standard Dates & Relationships):**
    *   Update `relationshipOptions`.
    *   Create the `getStandardDates` Cloud Function with date calculation logic for key relationships (e.g., Partner, Mother, Father) for a primary region.
    *   Integrate the Cloud Function call into the screen's data fetching.
    *   Display standard dates on the calendar.
    *   Update countdown logic to include standard dates.
    *   Implement enhanced visual markers (multi-dot, colors/icons).
    *   Refine multi-event display (e.g., list on tap).
*   **Phase 3 (UX Refinements):**
    *   Implement adding/editing custom dates directly from the calendar screen (optional).
    *   Address regional variations for standard dates (if prioritized).
    *   Consider adding recurrence options for custom dates.
    *   Performance optimizations for large numbers of profiles/dates.

**7. Mermaid Diagram (Data Flow Sketch):**

```mermaid
graph TD
    subgraph User Device (React Native App)
        A[Calendar Screen UI]
        B[ProfileForm UI]
        C[Data Fetching Logic]
        D[react-native-calendars]
        E[Countdown Component]
        F[State Management (Dates)]
    end

    subgraph Firebase Backend
        G[Firestore: significant_others Collection]
        H[Cloud Function: getStandardDates]
        I[Date Calculation Library]
    end

    A -- Needs Dates --> C
    B -- Saves Profile Data --> G
    C -- Fetches Profiles --> G
    G -- Returns Profiles (Birthdays, Anniversaries, Custom Dates, Relationships) --> C
    C -- Extracts Relationships & Year --> H
    H -- Uses --> I
    H -- Returns Standard Dates --> C
    C -- Combines All Dates --> F
    F -- Provides Marked Dates --> D
    F -- Provides Nearest Date --> E
    D -- Renders Calendar --> A
    E -- Renders Countdown --> A