/** @type {import('tailwindcss').Config} */
module.exports = {
  // NOTE: Update this to include the paths to all of your component files.
  content: ["./app/**/*.{js,jsx,ts,tsx}"],
  presets: [require("nativewind/preset")],
  darkMode: 'class', // Enable dark mode based on the 'dark' class
  theme: {
    extend: {
      colors: {
        // --- Pomegranate Core Colors ---
        primary: { // Deep, rich red of the pomegranate skin/juice
          DEFAULT: '#A3002B', // Rich pomegranate red
          500: '#A3002B',     // Rich pomegranate red
          600: '#800022',     // Darker pomegranate red
          dark: '#C70039',    // Slightly brighter, more vivid red for dark mode emphasis
        },
        accent: { // Brighter, juicier red of the arils
          DEFAULT: '#E5355F', // Ruby/pinkish-red aril color
          500: '#E5355F',     // Ruby/pinkish-red aril color
          600: '#C22A4F',     // Darker ruby red
          dark: '#FF507B',    // More vibrant pinkish-red for dark mode accent
        },
        // --- UI Element Colors (Pomegranate Inspired) ---
        background: { // Creamy pith / Deep dark red
          DEFAULT: '#FFFBF7', // Very light, warm off-white (like pomegranate pith)
          dark: '#1F1216',    // Very dark, desaturated red/brown (like deep shadows or dried skin)
        },
        card: { // Slightly off-white / Darker grey-red
          DEFAULT: '#FFFEFD', // Almost white, but warm (pith variation)
          dark: '#2F1E23',    // Dark grey with a hint of red, lighter than background.dark
        },
        'text-primary': { // Dark brownish-red / Light pinkish-cream
          DEFAULT: '#3D1C26', // Dark, slightly warm grey/brown (to complement reds)
          dark: '#FCEBEF',    // Light, slightly pinkish off-white
        },
        'text-secondary': { // Medium brownish-red / Lighter pinkish-grey
          DEFAULT: '#7A3E4F', // Medium, warm grey/brown
          dark: '#D7B8C0',    // Lighter, desaturated pinkish grey
        },
        border: { // Light pinkish-grey / Dark reddish-brown
          DEFAULT: '#EED9DF', // Light, desaturated pinkish grey
          dark: '#4D2A36',    // Darker, desaturated reddish brown
        },
        'input-background': {
          DEFAULT: '#FFFEFD', // Same as card light
          dark: '#2F1E23',    // Same as card dark
        },
        // --- Utility Colors ---
        disabled: { // Desaturated, muted tones
          DEFAULT: '#D1C5C9', // Desaturated pinkish grey
          dark: '#786A6E',    // Darker desaturated pinkish grey
        },
        error: { // Strong, alarming red (fits pomegranate theme well)
          DEFAULT: '#D90429',
          dark: '#B20021',
        },
        success: { // Kept standard green for universal understanding
          DEFAULT: '#22C55E',
          dark: '#16A34A',
        },
        // --- Semantic Colors (Pomegranate Tinted) ---
        holiday: { // Can use a primary red
          DEFAULT: '#C70039', // Using primary.dark for a strong holiday red
          dark: '#A3002B',    // Using primary.DEFAULT
        },
        birthday: { // Can use a cheerful accent red/pink
          DEFAULT: '#E5355F', // Using accent.DEFAULT
          dark: '#C22A4F',    // Using accent.600
        },
        anniversary: { // A deeper, more sophisticated purplish-red
          DEFAULT: '#5C002E', // Deep magenta/purple (like a very ripe pomegranate shade)
          dark: '#4A0025',
        },
        customDate: { // Another shade of purplish-red or magenta
          DEFAULT: '#7D003F', // A distinct pomegranate-esque purple/magenta
          dark: '#620031',
        },
      },
    },
  },
  plugins: [],
}