import { View, Text, TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { GeneralNote } from '@/functions/src/types/firestore';
import { Timestamp } from 'firebase/firestore'; // Import Timestamp

export interface GeneralNoteFormInputNote {
  note: string; // Include properties from GeneralNote
  date: Timestamp | null | undefined; // Allow null or undefined for form state
}

interface GeneralNoteFormInputProps {
  note: GeneralNoteFormInputNote; // Use the new interface
  onRemove: () => void;
  index: number;
}

export const GeneralNoteFormInput = ({
  note,
  onRemove,
  index,
}: GeneralNoteFormInputProps) => {
  const formatDate = (timestamp: any) => {
    if (!timestamp) return '';
    const date = timestamp.toDate();
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  return (
    <View className="flex-row items-center justify-between p-3 my-1 bg-white rounded-lg">
      <View className="flex-1">
        <Text className="text-base text-foreground" numberOfLines={2}>
          {note.note}
        </Text>
        {note.date ? (
          <Text className="mt-1 text-xs text-muted-foreground">
            {formatDate(note.date)}
          </Text>
        ) : null}
      </View>
      <View>
        <TouchableOpacity
          onPress={onRemove}
          accessibilityLabel="Remove note"
        >
          <Feather name="trash-2" size={20} color="#ef4444" />
        </TouchableOpacity>
      </View>
    </View>
  );
};