// types/firestore.ts

// Note: Using firebase-admin Timestamp for Cloud Functions
// import {Timestamp} from "firebase/firestore"; // Removed client-side import

// Interface for items in the wishlistItems array
export interface WishlistItem {
  item: string;
  notes?: string; // Made optional to match form schema
  link?: string; // Optional link
  dateAdded: Timestamp | null; // Allow null for dateAdded
}

// Interface for items in the pastGiftsGiven array
export interface PastGiftGiven {
  item: string;
  occasion?: string; // Optional occasion
  date: Timestamp | null; // Allow null for date
  reaction?: string; // Optional reaction
}

// Interface for items in the generalNotes array
export interface GeneralNote {
  note: string;
  date: Timestamp | null; // Allow null for date
}

// Interface for custom important dates
export interface CustomDate {
  id: string; // Unique ID for the custom date entry (e.g., UUID)
  name: string; // e.g., "First Date Anniversary"
  date: Timestamp | null; // The date of the custom event, allow null
  customDateMonthDay?: string; // "MM-DD" format
}

// Main interface for the Significant Other Profile document
export interface SignificantOtherProfile {
  userId: string; // Reference to the user who owns this profile
  profileId: string; // Unique ID for this profile document
  name: string;
  relationship: string; // e.g., "Partner", "Spouse"
  birthday?: Timestamp | null; // Optional, allow null
  anniversary?: Timestamp | null; // Optional, allow null
  birthdayMonthDay?: string; // Added: MM-DD format for querying
  anniversaryMonthDay?: string; // Added: MM-DD format for querying
  interests: string[];
  dislikes: string[];
  preferences: {
    favoriteColor?: string | null; // Allow null
    preferredStyle?: string | null; // Allow null
    favoriteBrands?: string[];
    budgetMin?: number;
    budgetMax?: number;
    // Add other preference fields as needed
  };
  sizes: {
    clothing?: string | null; // e.g., "M", "L", "UK 12", allow null
    shoe?: string | null; // e.g., "US 9", "EU 42", allow null
    // Add other size categories as needed
  };
  wishlistItems: WishlistItem[];
  pastGiftsGiven: PastGiftGiven[];
  generalNotes: GeneralNote[];
  customDates?: CustomDate[]; // Optional array of custom important dates
  // Add any other top-level fields planned
  createdAt: Timestamp; // Recommended: Timestamp for when profile was created
  updatedAt: Timestamp; // Recommended: Timestamp for last update
}

// Optional: Interface for the User document in the 'users' collection
export interface UserProfile {
  userId: string; // Should match the Firebase Auth UID
  email: string; // User's email
  displayName?: string; // Optional display name
  createdAt: Timestamp;
  // Add any other user-specific fields needed
}

// --- Zod Schema for Runtime Validation ---
import {z} from "zod";
import {Timestamp} from "firebase-admin/firestore"; // Use admin timestamp

// Helper schema for Firestore Timestamp
const zodTimestamp = z.custom<Timestamp>((val) => val instanceof Timestamp, {
  message: "Expected Firestore Timestamp",
});

const zodWishlistItem = z.object({
  item: z.string().min(1),
  notes: z.string().optional(),
  // Allow empty string or valid URL for link
  link: z.string().url().optional().or(z.literal("")),
  dateAdded: zodTimestamp.nullable(), // Allow null for dateAdded in Zod schema
});

const zodPastGiftGiven = z.object({
  item: z.string().min(1),
  occasion: z.string().optional(),
  date: zodTimestamp.nullable(),
  reaction: z.string().optional(),
});

const zodGeneralNote = z.object({
  note: z.string().min(1),
  date: zodTimestamp.nullable(),
});

const zodCustomDate = z.object({
  id: z.string().min(1),
  name: z.string().min(1),
  date: zodTimestamp.nullable(),
  customDateMonthDay: z.string().regex(/^\d{2}-\d{2}$/).optional(),
});

export const significantOtherProfileSchema = z.object({
  userId: z.string().min(1),
  profileId: z.string().min(1),
  name: z.string().min(1),
  relationship: z.string(), // Assuming required, adjust if optional
  birthday: zodTimestamp.nullable().optional(),
  anniversary: zodTimestamp.nullable().optional(),
  // MM-DD format validation
  birthdayMonthDay: z.string().regex(/^\d{2}-\d{2}$/).optional(),
  anniversaryMonthDay: z.string().regex(/^\d{2}-\d{2}$/).optional(),
  interests: z.array(z.string()),
  dislikes: z.array(z.string()),
  preferences: z.object({
    favoriteColor: z.string().nullable().optional(),
    preferredStyle: z.string().nullable().optional(),
    favoriteBrands: z.array(z.string()).optional(),
  }).passthrough(), // Allow other preference fields
  sizes: z.object({
    clothing: z.string().nullable().optional(),
    shoe: z.string().nullable().optional(),
  }).passthrough(), // Allow other size categories
  wishlistItems: z.array(zodWishlistItem),
  pastGiftsGiven: z.array(zodPastGiftGiven),
  generalNotes: z.array(zodGeneralNote),
  customDates: z.array(zodCustomDate).optional(),
  createdAt: zodTimestamp,
  updatedAt: zodTimestamp,
});

// Infer the TypeScript type from the Zod schema
export type SignificantOtherProfileValidated =
  z.infer<typeof significantOtherProfileSchema>;

// --- Zod Schema for User Profile ---
export const userProfileSchema = z.object({
  // Assuming userId is the document ID, not stored in the doc itself
  // Add fields expected in user_profiles based on SOProfileForPrompt usage
  interests: z.array(z.string()).optional().nullable(),
  dislikes: z.array(z.string()).optional().nullable(),
  preferences: z.record(z.string()).optional().nullable(),
  // Assuming pastGiftsGiven might be simpler here, maybe just strings?
  // Adjust if the structure is different (e.g., objects like in SOProfile)
  pastGiftsGiven: z.array(z.string()).optional().nullable(),
  // Add other expected fields like name, email if stored here
  // createdAt: zodTimestamp.optional(), // Optional if not always present
  // updatedAt: zodTimestamp.optional(),
}).passthrough(); // Allow other fields not explicitly defined

export type UserProfileValidated = z.infer<typeof userProfileSchema>;
