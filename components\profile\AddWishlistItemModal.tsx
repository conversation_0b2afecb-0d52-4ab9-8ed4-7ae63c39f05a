import React, { useState } from 'react';
import { Modal, View, Text, StyleSheet } from 'react-native';
import { WishlistItem } from '../../functions/src/types/firestore';
import Input from '../ui/Input';
import Button from '../ui/Button';

interface AddWishlistItemModalProps {
  isVisible: boolean;
  onClose: () => void;
  onAddItem: (newItem: Omit<WishlistItem, 'dateAdded'>) => void;
}

const AddWishlistItemModal: React.FC<AddWishlistItemModalProps> = ({
  isVisible,
  onClose,
  onAddItem,
}) => {
  const [item, setItem] = useState('');
  const [notes, setNotes] = useState('');
  const [link, setLink] = useState('');
  const [errors, setErrors] = useState({
    item: '',
    link: '',
  });

  const validateLink = (url: string) => {
    if (!url) return true;
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const handleSubmit = () => {
    const newErrors = {
      item: !item ? 'Item name is required' : '',
      link: link && !validateLink(link) ? 'Please enter a valid URL' : '',
    };

    setErrors(newErrors);

    if (!newErrors.item && !newErrors.link) {
      onAddItem({ item, notes, link });
      resetForm();
      onClose();
    }
  };

  const resetForm = () => {
    setItem('');
    setNotes('');
    setLink('');
    setErrors({ item: '', link: '' });
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      transparent={true}
      onRequestClose={handleClose}
    >
      <View style={styles.centeredView}>
        <View style={styles.modalView}>
          <Text style={styles.modalTitle}>Add Wishlist Item</Text>

          <Input
            label="Item Name*"
            value={item}
            onChangeText={setItem}
            error={errors.item}
            placeholder="e.g., Noise-cancelling headphones"
          />

          <Input
            label="Notes"
            value={notes}
            onChangeText={setNotes}
            placeholder="e.g., Prefers over-ear, specific model if known"
            multiline
          />

          <Input
            label="Link"
            value={link}
            onChangeText={setLink}
            error={errors.link}
            placeholder="e.g., https://www.amazon.com/headphones"
            keyboardType="url"
          />

          <View style={styles.buttonContainer}>
            <Button
              title="Cancel"
              onPress={handleClose}
              style={styles.button}
            />
            <Button
              title="Save Item"
              onPress={handleSubmit}
              style={styles.button}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalView: {
    width: '90%',
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  button: {
    flex: 1,
    marginHorizontal: 5,
  },
});

export default AddWishlistItemModal;