import React, { useEffect } from 'react';
import { View, ViewStyle, Text, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withSequence,
  withTiming,
  withDelay,
  Easing,
  interpolateColor,
} from 'react-native-reanimated';
import { useColorScheme } from 'nativewind'; // To get theme colors

// Assuming your tailwind config is imported and resolved if you need dynamic color lookup.
// For simplicity here, we'll use hardcoded fallbacks or expect resolved colors.
// import tailwindConfig from '../../tailwind.config.js'; // Path to your config
// import resolveConfig from 'tailwindcss/resolveConfig';
// const fullConfig = resolveConfig(tailwindConfig);

interface LoadingIndicatorProps {
  /**
   * Size of the loading indicator.
   * 'small': Smaller dots, tighter spacing.
   * 'large': Larger dots, more spacing.
   * @default 'large'
   */
  size?: 'small' | 'large';
  /**
   * Resolved color string (hex, rgb) for the indicator dots.
   * Defaults to the app's primary color based on the current theme.
   */
  color?: string;
  /**
   * Optional style for the main container View.
   */
  containerStyle?: ViewStyle;
  /**
   * Optional text to display below the indicator.
   */
  loadingText?: string;
  /**
   * ClassName for the loading text.
   */
  textClassName?: string;
}

const DOT_COUNT = 3;
const ANIMATION_DURATION_BASE = 500; // Base duration for one pulse

const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({
  size = 'large',
  color, // Will default based on theme
  containerStyle,
  loadingText,
  textClassName = '', // Default to empty string
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  // VC-1: Resolve default color from theme if not provided
  // These hex values should match your tailwind.config.js primary colors
  const defaultColor = isDark ? '#A3002B' /* primary.dark */ : '#A3002B' /* primary.DEFAULT */;
  const resolvedColor = color || defaultColor;

  const dotSize = size === 'large' ? 10 : 7;
  const dotMargin = size === 'large' ? 8 : 5;
  const animationScale = size === 'large' ? 1.3 : 1.15;

  // Create shared values for each dot's animation
  const opacities = Array.from({ length: DOT_COUNT }).map(() => useSharedValue(0.3));
  const scales = Array.from({ length: DOT_COUNT }).map(() => useSharedValue(1));

  useEffect(() => {
    opacities.forEach((opacity, index) => {
      opacity.value = withRepeat(
        withSequence(
          withDelay(
            index * (ANIMATION_DURATION_BASE / 2.5), // Stagger the start of each dot's animation
            withTiming(1, {
              duration: ANIMATION_DURATION_BASE,
              easing: Easing.bezier(0.4, 0, 0.6, 1), // Smooth in-out
            })
          ),
          withTiming(0.3, {
            duration: ANIMATION_DURATION_BASE,
            easing: Easing.bezier(0.4, 0, 0.6, 1),
          })
        ),
        -1, // Infinite repeat
        true // Reverse on repeat
      );
    });

    scales.forEach((scale, index) => {
        scale.value = withRepeat(
          withSequence(
            withDelay(
              index * (ANIMATION_DURATION_BASE / 2.5),
              withTiming(animationScale, {
                duration: ANIMATION_DURATION_BASE,
                easing: Easing.out(Easing.ease),
              })
            ),
            withTiming(1, {
              duration: ANIMATION_DURATION_BASE,
              easing: Easing.in(Easing.ease),
            })
          ),
          -1,
          true
        );
      });

    // Cleanup function to stop animations when component unmounts (important for performance)
    return () => {
      opacities.forEach((opacity) => (opacity.value = 0.3)); // Reset or cancel
      scales.forEach((scale) => (scale.value = 1));
      // In a more complex scenario with cancelAnimation:
      // opacities.forEach(opacity => cancelAnimation(opacity));
      // scales.forEach(scale => cancelAnimation(scale));
    };
  }, [opacities, scales, animationScale]);

  const containerClasses = 'flex-1 justify-center items-center py-4'; // Added py-4 for some breathing room

  const defaultTextClassName = `mt-3 text-sm ${isDark ? 'text-text-secondary-dark' : 'text-text-secondary'}`;

  return (
    <View className={containerClasses} style={containerStyle}>
      <View style={{ flexDirection: 'row' }}>
        {opacities.map((_, index) => {
          // eslint-disable-next-line react-hooks/rules-of-hooks
          const animatedDotStyle = useAnimatedStyle(() => {
            return {
              opacity: opacities[index].value,
              transform: [{ scale: scales[index].value }],
            };
          });
          return (
            <Animated.View
              key={index}
              style={[
                styles.dot,
                {
                  width: dotSize,
                  height: dotSize,
                  borderRadius: dotSize / 2,
                  backgroundColor: resolvedColor,
                  marginHorizontal: dotMargin,
                },
                animatedDotStyle,
              ]}
            />
          );
        })}
      </View>
      {loadingText && (
        <Text className={`${defaultTextClassName} ${textClassName}`}>
          {loadingText}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  dot: {
    // Base dot style, specific dimensions and color applied dynamically
  },
});

export default LoadingIndicator;