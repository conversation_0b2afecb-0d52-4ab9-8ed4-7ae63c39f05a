import { View, Text, TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { PastGiftGiven } from '../../functions/src/types/firestore';

interface PastGiftFormInputProps {
  gift: PastGiftGiven;
  onRemove: () => void;
  index: number;
}

export const PastGiftFormInput = ({
  gift,
  onRemove,
  index,
}: PastGiftFormInputProps) => {
  const formatDate = (timestamp: any) => {
    if (!timestamp) return '';
    const date = timestamp.toDate();
    return date.toLocaleDateString();
  };

  return (
    <View className="p-3 mb-2 border rounded-md border-border bg-background">
      <View className="flex-row justify-between">
        <View className="flex-1">
          <Text className="font-semibold text-foreground">
            {gift.item}
          </Text>
          {gift.occasion ? (
            <Text className="text-sm text-muted-foreground">
              {gift.occasion}
            </Text>
          ) : null}
          {gift.date ? (
            <Text className="text-xs text-muted-foreground">
              {formatDate(gift.date)}
            </Text>
          ) : null}
          {gift.reaction ? (
            <Text className="text-sm text-muted-foreground">
              Reaction: {gift.reaction}
            </Text>
          ) : null}
        </View>
        <TouchableOpacity
          onPress={onRemove}
          accessibilityLabel="Remove past gift"
        >
          <Feather name="trash-2" size={20} color="text-destructive" />
        </TouchableOpacity>
      </View>
    </View>
  );
};