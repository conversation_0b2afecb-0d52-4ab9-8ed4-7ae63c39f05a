# .github/workflows/ci.yml
name: Giftmi CI

on:
  push:
    branches: [ main ] # Or your primary branch name
  pull_request:
    branches: [ main ] # Or your primary branch name

jobs:
  build_and_check:
    runs-on: ubuntu-latest # Use the latest Ubuntu runner

    strategy:
      matrix:
        node-version: [18.x] # Specify Node.js version(s) to test against

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4 # Action to check out the code

    - name: Set up Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4 # Action to set up Node.js
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm' # Cache npm dependencies

    - name: Install dependencies
      run: npm ci # Use 'ci' for cleaner installs in CI environments

    - name: Run linter
      run: npm run lint # Assumes 'expo lint' is configured in package.json

    # Optional: Add test step later when tests exist
    # - name: Run tests
    #   run: npm run test -- --ci --watchAll=false # Example Jest command for CI