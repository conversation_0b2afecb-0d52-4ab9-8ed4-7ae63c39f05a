import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Control, Controller, FieldErrors } from 'react-hook-form';
import { Feather } from '@expo/vector-icons';
import Input from '@/components/ui/Input';
import Dropdown from '@/components/ui/Dropdown';
import ColorSwatchDropdown from '@/components/ui/ColorSwatchDropdown';
import { ProfileFormData } from './ProfileForm'; // Assuming ProfileFormData is exported from ProfileForm

interface BasicInfoSectionProps {
  control: Control<ProfileFormData>;
  errors: FieldErrors<ProfileFormData>;
  showDatePicker: (field: 'birthday' | 'anniversary') => void;
  birthdayValue: Date | null | undefined;
  anniversaryValue: Date | null | undefined;
}

const relationshipOptions = [
  { label: 'Select Relationship', value: '' },
  { label: 'Partner', value: 'Partner' },
  { label: 'Girlfriend', value: 'Girlfriend' },
  { label: 'Boyfriend', value: 'Boyfriend' },
  { label: 'Spouse', value: 'Spouse' },
  { label: 'Mother', value: 'Mother' },
  { label: 'Father', value: 'Father' },
  { label: 'Parent', value: 'Parent' },
  { label: 'Sibling', value: 'Sibling' },
  { label: 'Child', value: 'Child' },
  { label: 'Friend', value: 'Friend' },
  { label: 'Colleague', value: 'Colleague' },
  { label: 'Grandparent', value: 'Grandparent' },
  { label: 'Family Member', value: 'Family Member' },
  { label: 'Other', value: 'Other' },
];

const formatDate = (date: Date | null | undefined): string => {
  if (!date) return "Select Date";
  return date.toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};


const BasicInfoSection: React.FC<BasicInfoSectionProps> = ({
  control,
  errors,
  showDatePicker,
  birthdayValue,
  anniversaryValue,
}) => {
  return (
    <View className="gap-4">
      {/* Basic Information Fields */}
      <View>
        <Text className="mb-1 font-medium text-muted-foreground">Name *</Text>
        <Controller
          control={control}
          name="name"
          rules={{ required: 'Name is required' }}
          render={({ field: { onChange, onBlur, value } }) => (
            <Input
              placeholder="Enter name"
              onBlur={onBlur}
              onChangeText={onChange}
              value={value}
              error={errors.name?.message}
              accessibilityLabel="Name input"
            />
          )}
        />
      </View>

      <View>
        <Text className="mb-1 font-medium text-muted-foreground">Relationship *</Text>
        <Controller
          control={control}
          name="relationship"
          rules={{ required: 'Relationship is required' }}
          render={({ field: { onChange, value } }) => (
            <ColorSwatchDropdown
              options={relationshipOptions.map(opt => ({
                ...opt,
                color: '#6b7280' // Default gray color for all relationship options
              }))}
              selectedValue={value}
              onValueChange={onChange}
              error={errors.relationship?.message}
              accessibilityLabel="Relationship dropdown"
            />
          )}
        />
      </View>

      <View>
        <Text className="mb-1 font-medium text-muted-foreground">Birthday</Text>
        <TouchableOpacity
          onPress={() => showDatePicker('birthday')}
          className={`flex-row items-center justify-between p-3 border rounded-md ${errors.birthday ? 'border-destructive' : 'border-border'} bg-input-background`}
          accessibilityLabel="Select birthday"
          accessibilityRole="button"
        >
          <Text className={!birthdayValue ? 'text-muted-foreground' : 'text-foreground'}>
            {formatDate(birthdayValue)}
          </Text>
          <Feather name="calendar" size={20} color="#6b7280" />
        </TouchableOpacity>
        {errors.birthday && <Text className="mt-1 text-sm text-destructive">{errors.birthday.message}</Text>}
      </View>

      <View>
        <Text className="mb-1 font-medium text-muted-foreground">Anniversary</Text>
        <TouchableOpacity
          onPress={() => showDatePicker('anniversary')}
          className={`flex-row items-center justify-between p-3 border rounded-md ${errors.anniversary ? 'border-destructive' : 'border-border'} bg-input-background`}
          accessibilityLabel="Select anniversary"
          accessibilityRole="button"
        >
          <Text className={!anniversaryValue ? 'text-muted-foreground' : 'text-foreground'}>
            {formatDate(anniversaryValue)}
          </Text>
          <Feather name="calendar" size={20} color="#6b7280" />
        </TouchableOpacity>
        {errors.anniversary && <Text className="mt-1 text-sm text-destructive">{errors.anniversary.message}</Text>}
      </View>
    </View>
  );
};

export default BasicInfoSection;