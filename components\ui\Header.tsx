import React from 'react';
import { View, Text, ViewProps, TextStyle, ViewStyle } from 'react-native';

interface HeaderProps extends ViewProps {
  title: string;
  style?: ViewStyle; // Allow style overrides for the container
  titleStyle?: TextStyle; // Allow style overrides for the title
}

const Header: React.FC<HeaderProps> = ({ title, style, titleStyle, ...props }) => {
  // Basic header styling: padding, background, center items
  // Adjust based on style guide/theme as needed
  const headerContainerClasses = 'p-4 bg-card border-b border-border flex-row items-center'; // Use theme colors and consistent padding
  const headerTitleClasses = 'text-lg font-semibold text-text-primary'; // Use theme text color

  return (
    <View className={headerContainerClasses} style={style} {...props}>
      <Text className={headerTitleClasses} style={titleStyle}>
        {title}
      </Text>
      {/* Add other header elements like back buttons or icons later if needed */}
    </View>
  );
};

export default Header;