import React from 'react';
import { View, Text, Pressable, FlatList } from 'react-native';
import { format } from 'date-fns';
import { Feather } from '@expo/vector-icons';
import { CalendarEvent } from '@/hooks/useCalendarData'; // Import CalendarEvent type
import Button from '@/components/ui/Button'; // Assuming a Button component exists
import Animated, { FadeIn, FadeOut, SlideInRight } from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import resolveConfig from 'tailwindcss/resolveConfig';
import tailwindConfig from '../../tailwind.config'; // Adjust path as needed

// Resolve Tailwind config to access theme values
const fullConfig = resolveConfig(tailwindConfig);
const themeColors = fullConfig.theme?.colors || {};

// Helper to safely access nested theme colors
const getThemeColor = (path: string, fallback: string): string => {
  const keys = path.split('.');
  let color = themeColors as any;
  for (const key of keys) {
    if (color && typeof color === 'object' && key in color) {
      color = color[key];
    } else {
      return fallback; // Return fallback if path is invalid
    }
  }
  // Handle cases where the resolved value might be an object (e.g., primary: { 500: ... })
  if (typeof color === 'object' && 'DEFAULT' in color) {
    return color.DEFAULT;
  }
  if (typeof color === 'string') {
    return color;
  }
  return fallback; // Return fallback if final value is not a string
};

// Mapping for event types to theme colors (hex codes)
const eventColorMap: { [key in CalendarEvent['type']]: string } = {
  'Birthday': getThemeColor('birthday', '#22C55E'),
  'Anniversary': getThemeColor('anniversary', '#3B82F6'),
  'Custom Date': getThemeColor('customDate', '#A855F7'),
  'Holiday': getThemeColor('holiday', '#EF4444'),
};


// Define the return type for getEventTypeInfo
export interface EventTypeInfo {
  icon: keyof typeof Feather.glyphMap; // Use keyof typeof Feather.glyphMap for icon names
  color: string;
}

// Get icon and color for event type
export const getEventTypeInfo = (eventType: CalendarEvent['type'] | string): EventTypeInfo => {
  // Use the mapping for known event types
  if (eventType in eventColorMap) {
    return {
      icon: eventType === 'Birthday' ? 'gift' : eventType === 'Anniversary' ? 'heart' : eventType === 'Custom Date' ? 'calendar' : eventType === 'Holiday' ? 'star' : 'circle', // Assign appropriate icon
      color: eventColorMap[eventType as CalendarEvent['type']],
    };
  }

  // Default case for unknown types
  return { icon: 'circle', color: getThemeColor('disabled', '#9CA3AF') }; // Use disabled color for unknown
};

interface CalendarEventListProps {
  events: CalendarEvent[];
  onEventSelect: (event: CalendarEvent) => void;
}

const CalendarEventList: React.FC<CalendarEventListProps> = ({ events, onEventSelect }) => {

  const renderEventItem = ({ item }: { item: CalendarEvent }) => {
    const { icon, color } = getEventTypeInfo(item.type);

    return (
      <Animated.View 
        entering={SlideInRight.duration(300).delay(100 * events.indexOf(item))} 
        exiting={FadeOut.duration(200)}
        className="mb-3"
      >
        <View className="p-4 border shadow-sm rounded-xl bg-card dark:bg-card-dark border-border/30 dark:border-border-dark">
          <View className="flex-row items-center mb-2">
            <View className="p-2 rounded-full" style={{ backgroundColor: `${color}20` }}>
              <Feather name={icon} size={18} color={color} />
            </View>
            <Text className="ml-4 text-base font-semibold text-text-primary dark:text-text-primary-dark">{item.name}</Text>
          </View>
          <Text className="text-sm text-text-secondary dark:text-text-secondary-dark">
            {item.type} on {format(item.date, 'MMMM d, yyyy')}
          </Text>
          {item.type === 'Holiday' && item.description && (
             <Text className="mt-2 text-sm italic text-text-secondary dark:text-text-secondary-dark">{item.description}</Text>
          )}
          {/* Always render button, hook handles profileId */}
          <View className="mt-5">
            <Button
              title="Find Gift Ideas"
              variant="primary"
              className="w-full"
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                onEventSelect(item);
              }}
            />
          </View>
        </View>
      </Animated.View>
    );
  };

  return (
    <View className="mb-2 ">
      <Text className="mb-3 text-lg font-semibold text-text-primary dark:text-text-primary-dark">Events:</Text>
      {events.length > 0 ? (
        <FlatList
          data={events}
          renderItem={renderEventItem}
          keyExtractor={(item) => item.id}
          scrollEnabled={false} // Disable scrolling as it's inside a parent ScrollView
          ItemSeparatorComponent={() => <View className="h-2" />}
        />
      ) : (
        <Animated.View 
          entering={FadeIn.duration(300)} 
          exiting={FadeOut.duration(200)} 
          className="items-center py-6 border rounded-xl bg-card dark:bg-card-dark border-border/30 dark:border-border-dark"
        >
          <Feather name="calendar" size={24} color={getThemeColor('text-secondary', '#4B5563')} />
          <Text className="mt-2 text-base text-text-secondary dark:text-text-secondary-dark">No events for this date</Text>
        </Animated.View>
      )}
    </View>
  );
};

export default CalendarEventList;