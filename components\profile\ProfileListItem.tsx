// components/profile/ProfileListItem.tsx
import React, { useCallback } from 'react';
import { View, Text, TouchableOpacity, Platform } from 'react-native';
import { Feather } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import Animated, {
  FadeInDown,
  Layout,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  Easing,
} from 'react-native-reanimated';
import { SignificantOtherProfile } from '@/functions/src/types/firestore';
import { useColorScheme } from 'nativewind';

const AnimatedTouchableOpacity =
  Animated.createAnimatedComponent(TouchableOpacity);

// For the avatar initials
const getInitials = (name: string) => {
  const names = name.split(' ');
  let initials = names[0].substring(0, 1).toUpperCase();
  if (names.length > 1) {
    initials += names[names.length - 1].substring(0, 1).toUpperCase();
  }
  return initials;
};

interface ProfileListItemProps {
  item: SignificantOtherProfile;
  index: number;
  onPress: (profileId: string) => void;
  pressedCardId: Animated.SharedValue<string | null>;
}

const ProfileListItem: React.FC<ProfileListItemProps> = ({
  item,
  index,
  onPress,
  pressedCardId,
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  // This hook is now called at the top level of the ProfileListItem component
  const isPressed = useSharedValue(false);

  const cardAnimatedStyle = useAnimatedStyle(() => {
    const isCurrentlyPressed = pressedCardId.value === item.profileId;
    return {
      transform: [
        {
          scale: withSpring(isPressed.value || isCurrentlyPressed ? 0.97 : 1, {
            damping: 10,
            stiffness: 200,
          }),
        },
      ],
      shadowOpacity: withSpring(
        isPressed.value || isCurrentlyPressed ? 0.3 : isDark ? 0.15 : 0.1
      ),
      elevation: withSpring(isPressed.value || isCurrentlyPressed ? 10 : 5),
    };
  });

  const handlePress = useCallback(() => {
    onPress(item.profileId);
  }, [item.profileId, onPress]);

  return (
    <Animated.View
      // More sophisticated entrance: combines fade, slide, and slight spring
      entering={FadeInDown.delay(index * 120)
        .duration(400)
        .easing(Easing.out(Easing.quad))
        .springify()
        .damping(12)
        .stiffness(100)}
      layout={Layout.springify().damping(15).stiffness(150)} // For smooth reordering/deletion
      className="mx-1" // Add slight horizontal margin for better card separation with shadows
    >
      <AnimatedTouchableOpacity
        style={cardAnimatedStyle}
        onPress={handlePress}
        onPressIn={() => {
          isPressed.value = true;
        }}
        onPressOut={() => {
          isPressed.value = false;
        }}
        activeOpacity={1} // We handle visual feedback with reanimated
        className={`
          mb-4 rounded-2xl overflow-hidden shadow-lg
          ${isDark ? 'bg-card-dark border border-border-dark/30' : 'bg-card border border-border/20'}
        `}
        accessibilityLabel={`View ${item.name}'s profile`}
      >
        <View className="flex-row items-center gap-2 p-5 space-x-4">
          {/* Avatar/Initials */}
          <View
            className={`
              w-12 h-12 rounded-full items-center justify-center
              ${isDark ? 'bg-primary-dark/20' : 'bg-primary/10'}
            `}
          >
            <Text
              className={`
                text-2xl font-semibold
                ${isDark ? 'text-primary-dark' : 'text-primary'}
              `}
              allowFontScaling={false}
            >
              {getInitials(item.name)}
            </Text>
          </View>

          {/* Text Content */}
          <View className="flex-1">
            <Text
              numberOfLines={1}
              className={`
                text-xl font-bold
                ${isDark ? 'text-text-primary-dark' : 'text-text-primary'}
              `}
            >
              {item.name}
            </Text>
            <Text
              className={`
                text-sm
                ${isDark ? 'text-text-secondary-dark' : 'text-text-secondary'}
              `}
            >
              {item.relationship}
            </Text>
            {item.birthday && item.birthday.toDate && ( // Add check for toDate property
              <View className="flex-row items-center mt-1.5">
                <Feather
                  name="gift"
                  size={14}
                  className={`${isDark ? 'text-accent-dark' : 'text-accent'}`}
                />
                <Text
                  className={`
                    ml-1.5 text-xs
                    ${isDark ? 'text-accent-dark' : 'text-accent'}
                  `}
                >
                  {new Date(item.birthday.toDate()).toLocaleDateString(
                    undefined,
                    { month: 'long', day: 'numeric' }
                  )}
                </Text>
              </View>
            )}
          </View>

          {/* Chevron */}
          <Animated.View>
            <Feather
              name="chevron-right"
              size={26}
              className={`${isDark ? 'text-text-secondary-dark/70' : 'text-text-secondary/70'}`}
            />
          </Animated.View>
        </View>
      </AnimatedTouchableOpacity>
    </Animated.View>
  );
};

export default ProfileListItem;