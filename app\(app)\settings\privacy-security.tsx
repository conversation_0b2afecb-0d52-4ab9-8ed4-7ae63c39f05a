import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Linking,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Stack, useRouter } from 'expo-router'; // Added Stack
import { Feather } from '@expo/vector-icons';
import Card from '@/components/ui/Card';
import { useThemeManager } from '@/hooks/useThemeManager'; // To get current theme for icon color
import tailwindColors from 'tailwindcss/colors'; // For fallback or direct color access if needed

// Constants for URLs - better practice than magic strings
const PRIVACY_POLICY_URL = 'YOUR_PRIVACY_POLICY_URL_HERE';
const TERMS_OF_SERVICE_URL = 'YOUR_TERMS_OF_SERVICE_URL_HERE';

interface LegalLinkItemProps {
  label: string;
  onPress: () => void;
  isLast?: boolean;
  iconColor: string; // Pass themed color
}

const LegalLinkItem: React.FC<LegalLinkItemProps> = ({
  label,
  onPress,
  isLast = false,
  iconColor,
}) => (
  <TouchableOpacity
    className={`flex-row items-center justify-between py-3.5 ${ // Adjusted py for visual balance
      !isLast ? 'border-b border-border dark:border-border-dark' : ''
    }`}
    onPress={onPress}
    activeOpacity={0.7} // Standard active opacity
  >
    <Text className="text-base text-text-primary dark:text-text-primary-dark">
      {label}
    </Text>
    <Feather name="chevron-right" size={22} color={iconColor} />
  </TouchableOpacity>
);

export default function PrivacySecurityScreen() {
  const router = useRouter();
  const { colorScheme } = useThemeManager();

  // Determine chevron color based on theme, using text-secondary from Giftmi palette
  // Assuming your tailwind.config.js maps text-secondary colors
  const chevronIconColor =
    colorScheme === 'dark'
      ? tailwindColors.gray[400] // Example: text-secondary-dark
      : tailwindColors.gray[500]; // Example: text-secondary-light
  // For precise theme alignment, you'd ideally have these specific colors in your theme context
  // or directly use NativeWind classes if Feather supported them for 'color'.
  // For now, using tailwindColors directly as a close approximation.
  // If your `text-text-secondary` class is '#4B5563' (light) / '#D1D5DB' (dark) from config:
  // const chevronIconColor = colorScheme === 'dark' ? '#D1D5DB' : '#4B5563';


  return (
    <SafeAreaView style={{ flex: 1 }} edges={['bottom', 'left', 'right']}>
      <Stack.Screen options={{ title: 'Privacy & Security' }} />
      <ScrollView
        className="flex-1 bg-background dark:bg-background-dark"
        contentContainerClassName="p-4 sm:p-6" // Responsive padding
        showsVerticalScrollIndicator={false}
      >
        <Card className="p-5 mb-5">
          <Text className="mb-3 text-xl font-semibold text-text-primary dark:text-text-primary-dark">
            Data & LLM Privacy
          </Text>
          <View>
            <Text className="mb-1.5 text-base font-medium text-text-primary dark:text-text-primary-dark">
              AI & Your Data
            </Text>
            <Text className="mb-2 text-sm text-text-secondary dark:text-text-secondary-dark">
              To provide personalized gift recommendations, some anonymized or
              abstracted information from profiles (like interests and
              preferences, but never names or direct contact details) is
              securely processed by a third-party AI service. We are
              committed to protecting your privacy.
            </Text>
            <Text className="mt-1 text-sm text-text-secondary dark:text-text-secondary-dark">
              For comprehensive details on data usage and protection, please
              refer to our Privacy Policy.
            </Text>
          </View>
        </Card>

        <Card className="p-5">
          <Text className="mb-2 text-xl font-semibold text-text-primary dark:text-text-primary-dark">
            Legal Information
          </Text>
          <LegalLinkItem
            label="Privacy Policy"
            onPress={() => Linking.openURL(PRIVACY_POLICY_URL)}
            iconColor={chevronIconColor}
          />
          <LegalLinkItem
            label="Terms of Service"
            onPress={() => Linking.openURL(TERMS_OF_SERVICE_URL)}
            isLast={true}
            iconColor={chevronIconColor}
          />
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
}