{
  "compilerOptions": {
    "module": "NodeNext",
    "esModuleInterop": true,
    "moduleResolution": "nodenext",
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "outDir": "lib",
    "rootDir": "src", // <--- ADD THIS LINE (or ensure it's present)
    "sourceMap": true,
    "strict": true,
    "target": "es2017",
    "skipLibCheck": true
  },
  "compileOnSave": true,
  "include": [
    "src/**/*" // <-- Recommended change
  ]
  // Consider adding "exclude": ["node_modules", "lib"] if needed
}