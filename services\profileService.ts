import {
  collection,
  addDoc,
  getDocs,
  doc,
  getDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  Timestamp,
  serverTimestamp, // Use serverTimestamp for consistency if preferred, else Timestamp.now()
} from 'firebase/firestore';
import { db } from '../firebaseConfig'; // Assuming db is exported from firebaseConfig.ts
import { SignificantOtherProfile } from '../functions/src/types/firestore'; // Import the main profile type

// Define the collection name as a constant for consistency
const COLLECTION_NAME = 'significant_others';

// Type for data needed when adding a new profile
// Excludes fields managed internally (userId, profileId, timestamps, arrays initialized empty)
export type AddProfileData = Omit<SignificantOtherProfile,
  'userId' |
  'profileId' |
  'createdAt' |
  'updatedAt'
> & {
  wishlistItems?: Array<{ // Include wishlistItems as optional
    item: string;
    link?: string;
    notes?: string;
    price?: number;
    priority?: 'low' | 'medium' | 'high';
    isPurchased?: boolean;
  }>;
  pastGiftsGiven?: Array<{
    item: string;
    occasion?: string;
    date?: Timestamp | null;
    reaction?: string;
  }>;
  generalNotes?: Array<{
    note: string;
    date?: Timestamp | null;
  }>;
};

/**
 * Adds a new Significant Other profile to Firestore.
 * @param userId - The ID of the user creating the profile.
 * @param profileData - The profile data to add (matching AddProfileData type).
 * @returns The ID of the newly created Firestore document.
 */
export const addSignificantOther = async (
  userId: string,
  profileData: AddProfileData
): Promise<string> => {
  console.log(`Adding profile for user: ${userId}`);
  try {
    const docRef = await addDoc(collection(db, COLLECTION_NAME), {
      ...profileData,
      userId: userId, // Associate with the user
      // Initialize arrays/objects if not included in AddProfileData implicitly
      interests: profileData.interests || [],
      dislikes: profileData.dislikes || [],
      preferences: profileData.preferences || {},
      sizes: profileData.sizes || {},
      wishlistItems: profileData.wishlistItems || [], // Use provided data or initialize empty
      pastGiftsGiven: profileData.pastGiftsGiven || [], // Use provided data or initialize empty
      generalNotes: profileData.generalNotes || [], // Use provided data or initialize empty
      createdAt: Timestamp.now(), // Set creation timestamp
      updatedAt: Timestamp.now(), // Set initial update timestamp
    });
    console.log(`Profile added successfully with ID: ${docRef.id}`);
    return docRef.id;
  } catch (error) {
    console.error('Error adding significant other profile:', error);
    // Rethrow the error or handle it as needed by the calling UI
    throw new Error('Failed to add significant other profile.');
  }
};

/**
 * Fetches all Significant Other profiles associated with a specific user.
 * @param userId - The ID of the user whose profiles to fetch.
 * @returns An array of Significant Other profiles.
 */
export const getSignificantOthers = async (userId: string): Promise<SignificantOtherProfile[]> => {
  console.log(`Fetching profiles for user: ${userId}`);
  const profiles: SignificantOtherProfile[] = [];
  try {
    const q = query(collection(db, COLLECTION_NAME), where('userId', '==', userId));
    const querySnapshot = await getDocs(q);
    querySnapshot.forEach((doc) => {
      // Combine document data with its ID
      profiles.push({
        profileId: doc.id,
        ...(doc.data() as Omit<SignificantOtherProfile, 'profileId'>), // Type assertion
      });
    });
    console.log(`Found ${profiles.length} profiles for user ${userId}`);
    return profiles;
  } catch (error) {
    console.error('Error fetching significant other profiles:', error);
    throw new Error('Failed to fetch significant other profiles.');
  }
};

/**
 * Fetches a single Significant Other profile by its ID, ensuring it belongs to the user.
 * @param userId - The ID of the user requesting the profile.
 * @param profileId - The ID of the profile document to fetch.
 * @returns The profile data or null if not found or not owned by the user.
 */
export const getSignificantOtherById = async (
  userId: string,
  profileId: string
): Promise<SignificantOtherProfile | null> => {
  console.log(`Fetching profile ID: ${profileId} for user: ${userId}`);
  try {
    const docRef = doc(db, COLLECTION_NAME, profileId);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      const data = docSnap.data() as Omit<SignificantOtherProfile, 'profileId'>;
      // IMPORTANT: Verify ownership
      if (data.userId === userId) {
        console.log(`Profile ${profileId} found and verified for user ${userId}`);
        return { profileId: docSnap.id, ...data };
      } else {
        console.warn(`User ${userId} attempted to access profile ${profileId} owned by ${data.userId}`);
        return null; // Not owned by the requesting user
      }
    } else {
      console.log(`Profile with ID: ${profileId} not found.`);
      return null; // Document does not exist
    }
  } catch (error) {
    console.error(`Error fetching significant other profile ${profileId}:`, error);
    throw new Error('Failed to fetch significant other profile.');
  }
};

/**
 * Updates an existing Significant Other profile, ensuring ownership.
 * @param userId - The ID of the user updating the profile.
 * @param profileId - The ID of the profile document to update.
 * @param updatedData - An object containing the fields to update (Partial<SignificantOtherProfile>).
 */
export const updateSignificantOther = async (
  userId: string,
  profileId: string,
  updatedData: Partial<Omit<SignificantOtherProfile, 'userId' | 'profileId' | 'createdAt'>> // Exclude fields that shouldn't be directly updated
): Promise<void> => {
  console.log(`Updating profile ID: ${profileId} for user: ${userId}`);
  const docRef = doc(db, COLLECTION_NAME, profileId);

  try {
    // Optional: Verify ownership before updating (more secure)
    const docSnap = await getDoc(docRef);
    if (!docSnap.exists() || docSnap.data()?.userId !== userId) {
      console.error(`Error updating profile ${profileId}: Not found or user ${userId} does not have permission.`);
      throw new Error('Profile not found or permission denied.');
    }

    // Proceed with the update

    await updateDoc(docRef, {
      ...updatedData, // Use the original updatedData
      updatedAt: Timestamp.now(), // Update the timestamp
    });
    console.log(`Profile ${profileId} updated successfully by user ${userId}`);
  } catch (error) {
    console.error(`Error updating significant other profile ${profileId}:`, error);
    // Rethrow or handle as needed
    if (error instanceof Error && error.message.includes('permission denied')) {
         throw error; // Keep specific error message
    }
    throw new Error('Failed to update significant other profile.');
  }
};

/**
 * Deletes a Significant Other profile, ensuring ownership.
 * @param userId - The ID of the user deleting the profile.
 * @param profileId - The ID of the profile document to delete.
 */
export const deleteSignificantOther = async (
  userId: string,
  profileId: string
): Promise<void> => {
  console.log(`Attempting to delete profile ID: ${profileId} by user: ${userId}`);
  const docRef = doc(db, COLLECTION_NAME, profileId);

  try {
    // IMPORTANT: Verify ownership before deleting
    const docSnap = await getDoc(docRef);
    if (!docSnap.exists()) {
        console.warn(`Profile ${profileId} not found for deletion.`);
        // Depending on requirements, either throw an error or return silently
        throw new Error('Profile not found.');
        // return;
    }

    const data = docSnap.data();
    if (data?.userId !== userId) {
      console.error(`Error deleting profile ${profileId}: User ${userId} does not have permission.`);
      throw new Error('Permission denied to delete this profile.');
    }

    // Proceed with deletion
    await deleteDoc(docRef);
    console.log(`Profile ${profileId} deleted successfully by user ${userId}`);
  } catch (error) {
    console.error(`Error deleting significant other profile ${profileId}:`, error);
     if (error instanceof Error && (error.message.includes('permission denied') || error.message.includes('Profile not found'))) {
         throw error; // Keep specific error message
    }
    throw new Error('Failed to delete significant other profile.');
  }
};